# Multi-Device Touch Event Synchronization System

A comprehensive system for synchronizing touch events across multiple Android devices with proper coordinate scaling, error handling, and real-time communication.

## 🚀 Features

- **Real-time Event Synchronization**: Capture touch events from a master device and execute them on multiple slave devices simultaneously
- **Intelligent Coordinate Scaling**: Automatically scale coordinates between devices with different screen resolutions
- **Robust Architecture**: Modular design with clear separation between server, master client, and slave clients
- **Comprehensive Event Support**: Handle tap, swipe, scroll, key press, and text input events
- **Error Handling & Recovery**: Automatic reconnection, health monitoring, and graceful error recovery
- **Performance Monitoring**: Real-time statistics and logging for debugging and optimization
- **Easy Configuration**: Flexible configuration system with command-line overrides

## 📋 System Architecture

The system consists of three main components:

### 1. Central Server (`SyncServer`)
- Manages WebSocket connections from master and slave devices
- Broadcasts events from master to all connected slaves
- Handles device registration and resolution information
- Provides health monitoring and statistics

### 2. Master Client (`MasterClient`)
- Connects to a master Android device via ADB
- Captures touch events using `getevent`
- Processes and normalizes event coordinates
- Sends events to the central server

### 3. Slave Client (`SlaveClient`)
- Connects multiple slave Android devices to the server
- Receives events from the server
- Scales coordinates for each device's resolution
- Executes events using ADB `input` commands

## 🔧 Requirements

### Software Requirements
- Python 3.7+
- ADB (Android Debug Bridge) installed and in PATH
- WebSockets library: `pip install websockets`

### Hardware Requirements
- Multiple Android devices (physical or emulators)
- USB connections or network ADB access
- Devices with USB debugging enabled

## 📦 Installation

1. **Clone or download the system:**
   ```bash
   # If you have the code in a repository
   git clone <repository-url>
   cd device_sync_system
   
   # Or if you have the files locally, navigate to the directory
   cd device_sync_system
   ```

2. **Install dependencies:**
   ```bash
   pip install websockets
   ```

3. **Verify ADB installation:**
   ```bash
   adb version
   adb devices
   ```

4. **Make scripts executable (Linux/Mac):**
   ```bash
   chmod +x examples/*.py
   ```

## 🚀 Quick Start

### Step 1: Start the Central Server
```bash
python examples/run_server.py
```

The server will start and display connection URLs for master and slave devices.

### Step 2: Connect Master Device
```bash
python examples/run_master.py
```

This will:
- Discover available Android devices
- Prompt you to select the master device
- Start capturing touch events from the selected device

### Step 3: Connect Slave Devices
```bash
python examples/run_slaves.py
```

This will:
- Discover available Android devices (excluding the master)
- Prompt you to select slave devices
- Start multiple slave clients to execute events

### Step 4: Test the System
1. Interact with the master device (tap, swipe, etc.)
2. Watch the events being executed on all slave devices
3. Monitor the console output for real-time statistics

## 🔧 Configuration

### Command Line Options

**Server:**
```bash
python examples/run_server.py --host 0.0.0.0 --port 8765 --log-level DEBUG
```

**Master Client:**
```bash
python examples/run_master.py --device emulator-5554 --server ws://*************:8765/master
```

**Slave Clients:**
```bash
python examples/run_slaves.py --devices emulator-5556,emulator-5558 --exclude-master emulator-5554
```

### Configuration File

The system uses a JSON configuration file that can be customized:

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 8765,
    "max_connections": 100,
    "heartbeat_interval": 30.0,
    "connection_timeout": 60.0
  },
  "client": {
    "server_url": "ws://127.0.0.1:8765",
    "reconnect_attempts": 5,
    "reconnect_delay": 5.0,
    "heartbeat_interval": 30.0,
    "connection_timeout": 30.0
  },
  "coordinates": {
    "enable_scaling": true,
    "fallback_to_raw": true,
    "clamp_to_bounds": true,
    "coordinate_validation": true
  },
  "logging": {
    "level": "INFO",
    "file_enabled": true,
    "file_path": "logs/device_sync.log",
    "console_enabled": true
  }
}
```

## 📱 Device Setup

### Android Emulator Setup
1. **Start multiple emulators:**
   ```bash
   emulator -avd Pixel_4_API_30 -port 5554
   emulator -avd Pixel_4_API_30 -port 5556
   emulator -avd Pixel_4_API_30 -port 5558
   ```

2. **Verify devices are detected:**
   ```bash
   adb devices
   ```

### Physical Device Setup
1. **Enable Developer Options:**
   - Go to Settings > About Phone
   - Tap "Build Number" 7 times

2. **Enable USB Debugging:**
   - Go to Settings > Developer Options
   - Enable "USB Debugging"

3. **Connect and authorize:**
   ```bash
   adb devices
   # Accept authorization prompt on device
   ```

## 🔍 Troubleshooting

### Common Issues

**1. "No devices found"**
- Ensure ADB is installed and in PATH
- Check USB connections
- Verify USB debugging is enabled
- Run `adb devices` to confirm device detection

**2. "Permission denied" errors**
- Ensure devices are authorized for USB debugging
- Try running with elevated permissions if needed
- Check ADB server status: `adb kill-server && adb start-server`

**3. "Connection failed" errors**
- Verify the server is running and accessible
- Check firewall settings
- Ensure correct server URL and port

**4. "Coordinate scaling issues"**
- Check device resolution detection in logs
- Verify input device capabilities are detected correctly
- Enable debug logging for detailed coordinate information

### Debug Mode

Enable detailed logging for troubleshooting:
```bash
python examples/run_server.py --log-level DEBUG
python examples/run_master.py --log-level DEBUG
python examples/run_slaves.py --log-level DEBUG
```

## 📊 Monitoring and Statistics

The system provides real-time monitoring:

- **Server Statistics**: Connection counts, event processing rates, uptime
- **Master Client**: Events captured, events sent, connection status
- **Slave Clients**: Events received, events executed, success rates

Statistics are displayed in the console and logged to files for analysis.

## 🔧 Advanced Usage

### Custom Server URL
```bash
# Connect to remote server
python examples/run_master.py --server ws://*************:8765/master
python examples/run_slaves.py --server ws://*************:8765/slave
```

### Specific Device Selection
```bash
# Use specific devices
python examples/run_master.py --device emulator-5554
python examples/run_slaves.py --devices emulator-5556,emulator-5558,emulator-5560
```

### Network ADB
```bash
# Connect to devices over network
adb connect 192.168.1.101:5555
adb connect 192.168.1.102:5555
```

## 🤝 Contributing

This system is designed to be modular and extensible. Key areas for enhancement:

- Additional event types (pinch, rotate, etc.)
- GUI management interface
- Performance optimizations
- Cross-platform support
- Event recording and playback

## 📄 License

This project is provided as-is for educational and development purposes.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Enable debug logging for detailed information
3. Review log files in the `logs/` directory
4. Verify ADB and device connectivity
