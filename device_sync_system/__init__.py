"""
Multi-Device Touch Event Synchronization System

A comprehensive system for synchronizing touch events across multiple Android devices
with proper coordinate scaling, error handling, and real-time communication.
"""

__version__ = "1.0.0"
__author__ = "Device Sync System"

from .utils.logger import initialize_logging
from .config.settings import get_config

# Initialize logging when the package is imported
initialize_logging()
