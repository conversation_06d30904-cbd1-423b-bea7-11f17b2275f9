"""
Configuration settings for the multi-device synchronization system.
"""

import os
import json
from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any
import logging


@dataclass
class ServerConfig:
    """Central server configuration."""
    host: str = "0.0.0.0"
    port: int = 8765
    max_connections: int = 100
    heartbeat_interval: float = 30.0
    connection_timeout: float = 60.0
    message_queue_size: int = 1000


@dataclass
class ClientConfig:
    """Client configuration for master and slave devices."""
    server_url: str = "ws://127.0.0.1:8765"
    reconnect_attempts: int = 5
    reconnect_delay: float = 5.0
    heartbeat_interval: float = 30.0
    connection_timeout: float = 30.0
    command_timeout: float = 10.0


@dataclass
class CoordinateConfig:
    """Coordinate scaling and transformation configuration."""
    enable_scaling: bool = True
    fallback_to_raw: bool = True
    clamp_to_bounds: bool = True
    scaling_precision: int = 2
    coordinate_validation: bool = True


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
    file_enabled: bool = True
    file_path: str = "logs/device_sync.log"
    console_enabled: bool = True
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class DeviceConfig:
    """Device-specific configuration."""
    adb_timeout: float = 10.0
    device_discovery_interval: float = 30.0
    health_check_interval: float = 60.0
    max_error_count: int = 5
    input_event_timeout: float = 5.0


@dataclass
class SystemConfig:
    """Complete system configuration."""
    server: ServerConfig
    client: ClientConfig
    coordinates: CoordinateConfig
    logging: LoggingConfig
    device: DeviceConfig
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration values."""
        if self.server.port < 1 or self.server.port > 65535:
            raise ValueError(f"Invalid server port: {self.server.port}")
        
        if self.client.reconnect_attempts < 0:
            raise ValueError(f"Invalid reconnect attempts: {self.client.reconnect_attempts}")
        
        if self.logging.level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError(f"Invalid logging level: {self.logging.level}")


class ConfigManager:
    """Manages system configuration loading and saving."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/device_sync_config.json"
        self._config: Optional[SystemConfig] = None
    
    def load_config(self) -> SystemConfig:
        """Load configuration from file or create default."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Create config objects from loaded data
                server_config = ServerConfig(**config_data.get('server', {}))
                client_config = ClientConfig(**config_data.get('client', {}))
                coordinates_config = CoordinateConfig(**config_data.get('coordinates', {}))
                logging_config = LoggingConfig(**config_data.get('logging', {}))
                device_config = DeviceConfig(**config_data.get('device', {}))
                
                self._config = SystemConfig(
                    server=server_config,
                    client=client_config,
                    coordinates=coordinates_config,
                    logging=logging_config,
                    device=device_config
                )
                
                logging.info(f"Configuration loaded from {self.config_file}")
                
            except Exception as e:
                logging.warning(f"Failed to load config from {self.config_file}: {e}")
                logging.info("Using default configuration")
                self._config = self._create_default_config()
        else:
            logging.info(f"Config file {self.config_file} not found, using defaults")
            self._config = self._create_default_config()
            self.save_config()  # Save default config for future reference
        
        return self._config
    
    def save_config(self) -> bool:
        """Save current configuration to file."""
        if not self._config:
            logging.error("No configuration to save")
            return False
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # Convert config to dictionary
            config_dict = {
                'server': asdict(self._config.server),
                'client': asdict(self._config.client),
                'coordinates': asdict(self._config.coordinates),
                'logging': asdict(self._config.logging),
                'device': asdict(self._config.device)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_dict, f, indent=2)
            
            logging.info(f"Configuration saved to {self.config_file}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to save config to {self.config_file}: {e}")
            return False
    
    def _create_default_config(self) -> SystemConfig:
        """Create default configuration."""
        return SystemConfig(
            server=ServerConfig(),
            client=ClientConfig(),
            coordinates=CoordinateConfig(),
            logging=LoggingConfig(),
            device=DeviceConfig()
        )
    
    def get_config(self) -> SystemConfig:
        """Get current configuration."""
        if not self._config:
            return self.load_config()
        return self._config
    
    def update_config(self, **kwargs) -> bool:
        """Update configuration with new values."""
        if not self._config:
            self.load_config()
        
        try:
            for section, values in kwargs.items():
                if hasattr(self._config, section):
                    section_config = getattr(self._config, section)
                    for key, value in values.items():
                        if hasattr(section_config, key):
                            setattr(section_config, key, value)
                        else:
                            logging.warning(f"Unknown config key: {section}.{key}")
                else:
                    logging.warning(f"Unknown config section: {section}")
            
            self._config._validate_config()
            return True
            
        except Exception as e:
            logging.error(f"Failed to update configuration: {e}")
            return False


# Global configuration instance
_config_manager = ConfigManager()

def get_config() -> SystemConfig:
    """Get the global configuration instance."""
    return _config_manager.get_config()

def save_config() -> bool:
    """Save the global configuration."""
    return _config_manager.save_config()

def update_config(**kwargs) -> bool:
    """Update the global configuration."""
    return _config_manager.update_config(**kwargs)
