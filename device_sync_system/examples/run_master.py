#!/usr/bin/env python3
"""
Master Device Client startup script for the multi-device synchronization system.

This script connects to a master Android device, captures touch events,
and sends them to the central server for broadcasting to slave devices.
"""

import asyncio
import argparse
import signal
import sys
import os

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.master_client import MasterClient
from utils.logger import initialize_logging, get_logger
from utils.device_manager import get_device_manager
from config.settings import get_config, update_config


def setup_signal_handlers(client: MasterClient):
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down master client...")
        asyncio.create_task(client.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def select_master_device() -> str:
    """Interactive device selection for master device."""
    device_manager = get_device_manager()
    
    print("\n" + "="*60)
    print("🎯 MASTER DEVICE SELECTION")
    print("="*60)
    print("Please select which Android device will be the MASTER device.")
    print("The master device captures touch events and sends them to slave devices.")
    print()
    
    # Discover available devices
    devices = await device_manager.discover_devices(force_refresh=True)
    
    if not devices:
        print("❌ No Android devices found!")
        print("   Please ensure:")
        print("   • Android emulators are running")
        print("   • Physical devices are connected and authorized")
        print("   • ADB is properly installed and in PATH")
        print("   • USB debugging is enabled on devices")
        return None
    
    print(f"📱 Found {len(devices)} available device(s):")
    for i, device in enumerate(devices, 1):
        # Get device info for better display
        device_info = await device_manager.get_device_info(device)
        if device_info:
            print(f"   {i}. {device} - {device_info.model} (Android {device_info.android_version})")
        else:
            print(f"   {i}. {device} - (Unable to get device info)")
    
    print("\n💡 Examples of valid input:")
    print("   • 1 (select first device)")
    print("   • emulator-5554")
    print("   • device_serial_number")
    
    while True:
        print(f"\n🔍 Select master device:")
        print(f"   Enter number (1-{len(devices)}) or device ID:")
        
        user_input = input("Master device: ").strip()
        
        if not user_input:
            print("❌ Please enter a device number or ID")
            continue
        
        # Check if input is a number
        try:
            device_index = int(user_input) - 1
            if 0 <= device_index < len(devices):
                selected_device = devices[device_index]
                print(f"✅ Selected master device: {selected_device}")
                return selected_device
            else:
                print(f"❌ Invalid device number. Please enter 1-{len(devices)}")
                continue
        except ValueError:
            pass
        
        # Check if input is a valid device ID
        if user_input in devices:
            print(f"✅ Selected master device: {user_input}")
            return user_input
        else:
            print(f"❌ Invalid device ID: '{user_input}'")
            print(f"   Available devices: {', '.join(devices)}")
            
            # Ask if user wants to retry or exit
            retry = input("\n🔄 Try again? (y/n): ").strip().lower()
            if retry not in ['y', 'yes']:
                print("👋 Exiting...")
                return None


async def main():
    """Main function to start the master client."""
    parser = argparse.ArgumentParser(
        description='Master Device Client for Multi-Device Touch Event Synchronization',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                          # Interactive device selection
  %(prog)s --device emulator-5554   # Use specific device
  %(prog)s --server ws://*************:8765/master  # Connect to remote server
  %(prog)s --log-level DEBUG        # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--device',
        help='Master device ID (if not provided, will prompt for selection)'
    )
    parser.add_argument(
        '--server',
        help='Server URL (default: from config)'
    )
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default=None,
        help='Logging level (default: from config)'
    )
    
    args = parser.parse_args()
    
    try:
        # Update configuration if command line arguments provided
        config_updates = {}
        if args.log_level:
            config_updates['logging'] = {'level': args.log_level}
        
        if config_updates:
            update_config(**config_updates)
        
        # Initialize logging
        initialize_logging()
        logger = get_logger("MasterMain")
        
        # Print startup banner
        print_startup_banner()
        
        # Select device
        device_id = args.device
        if not device_id:
            device_id = await select_master_device()
            if not device_id:
                print("❌ No master device selected. Exiting...")
                return
        
        # Validate device exists
        device_manager = get_device_manager()
        available_devices = await device_manager.discover_devices()
        if device_id not in available_devices:
            print(f"❌ Device '{device_id}' not found in available devices: {available_devices}")
            return
        
        # Create and configure client
        server_url = args.server
        client = MasterClient(device_id, server_url)
        
        # Setup signal handlers for graceful shutdown
        setup_signal_handlers(client)
        
        logger.info(f"Starting Master Client for device: {device_id}")
        print(f"\n🚀 Starting Master Client for device: {device_id}")
        print("="*60)
        print("📱 Touch events from this device will be captured and sent to slave devices.")
        print("🎯 Start interacting with the device to see events being synchronized!")
        print("="*60)
        print()
        
        # Start the client
        await client.start()
        
    except KeyboardInterrupt:
        print("\n🛑 Master client stopped by user")
    except Exception as e:
        print(f"\n❌ Master client failed to start: {e}")
        logger = get_logger("MasterMain")
        logger.critical(f"Master client startup failed: {e}", exc_info=True)
        sys.exit(1)


def print_startup_banner():
    """Print startup banner with system information."""
    print("\n" + "="*70)
    print("🎮 MASTER DEVICE CLIENT")
    print("="*70)
    print("This client captures touch events from the master Android device")
    print("and sends them to the central server for broadcasting to slave devices.")
    print()
    print("📋 Master Client Features:")
    print("  • Real-time touch event capture via ADB getevent")
    print("  • Automatic coordinate scaling and normalization")
    print("  • Support for tap, swipe, and key events")
    print("  • Robust connection handling with auto-reconnect")
    print("  • Comprehensive event logging and statistics")
    print()
    print("🔧 Requirements:")
    print("  • ADB installed and in PATH")
    print("  • Android device connected and authorized")
    print("  • USB debugging enabled on the device")
    print("  • Central server running and accessible")
    print("="*70)
    print()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)
