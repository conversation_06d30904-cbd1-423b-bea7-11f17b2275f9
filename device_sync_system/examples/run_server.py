#!/usr/bin/env python3
"""
Central Server startup script for the multi-device synchronization system.

This script starts the central server that manages connections between
master and slave devices and handles event broadcasting.
"""

import asyncio
import argparse
import signal
import sys
import os

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from device_sync_system.core.server import Sync<PERSON>erver
from device_sync_system.utils.logger import initialize_logging, get_logger
from device_sync_system.config.settings import get_config, update_config


def setup_signal_handlers(server: SyncServer):
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down server...")
        asyncio.create_task(server.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main function to start the synchronization server."""
    parser = argparse.ArgumentParser(
        description='Central Server for Multi-Device Touch Event Synchronization',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                          # Start server with default settings
  %(prog)s --host 0.0.0.0 --port 8765  # Start on specific host/port
  %(prog)s --log-level DEBUG        # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--host', 
        default=None,
        help='Server host address (default: from config)'
    )
    parser.add_argument(
        '--port', 
        type=int,
        default=None,
        help='Server port (default: from config)'
    )
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default=None,
        help='Logging level (default: from config)'
    )
    parser.add_argument(
        '--config-file',
        help='Path to configuration file'
    )
    
    args = parser.parse_args()
    
    try:
        # Update configuration if command line arguments provided
        config_updates = {}
        if args.log_level:
            config_updates['logging'] = {'level': args.log_level}
        if args.host:
            config_updates['server'] = {'host': args.host}
        if args.port:
            config_updates['server'] = config_updates.get('server', {})
            config_updates['server']['port'] = args.port
        
        if config_updates:
            update_config(**config_updates)
        
        # Initialize logging
        initialize_logging()
        logger = get_logger("ServerMain")
        
        # Print startup banner
        print_startup_banner()
        
        # Create and configure server
        config = get_config()
        server = SyncServer(
            host=args.host or config.server.host,
            port=args.port or config.server.port
        )
        
        # Setup signal handlers for graceful shutdown
        setup_signal_handlers(server)
        
        logger.info("Starting Device Synchronization Server...")
        
        # Start the server
        await server.start()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        logger = get_logger("ServerMain")
        logger.critical(f"Server startup failed: {e}", exc_info=True)
        sys.exit(1)


def print_startup_banner():
    """Print startup banner with system information."""
    print("\n" + "="*70)
    print("🚀 MULTI-DEVICE TOUCH EVENT SYNCHRONIZATION SERVER")
    print("="*70)
    print("This server coordinates touch events between master and slave devices.")
    print("Master devices capture touch events and send them to this server.")
    print("Slave devices receive events from this server and execute them.")
    print()
    print("📋 Server Features:")
    print("  • Real-time event broadcasting")
    print("  • Automatic coordinate scaling")
    print("  • Device connection management")
    print("  • Health monitoring and statistics")
    print("  • Robust error handling and recovery")
    print()
    print("🔧 Usage:")
    print("  1. Start this server first")
    print("  2. Connect master device using run_master.py")
    print("  3. Connect slave devices using run_slaves.py")
    print("="*70)
    print()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)
