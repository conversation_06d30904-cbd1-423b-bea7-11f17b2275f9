#!/usr/bin/env python3
"""
Slave Devices Client startup script for the multi-device synchronization system.

This script connects to multiple slave Android devices, receives events from
the central server, and executes them with proper coordinate scaling.
"""

import asyncio
import argparse
import signal
import sys
import os
from typing import List, Dict

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.slave_client import SlaveClient
from utils.logger import initialize_logging, get_logger
from utils.device_manager import get_device_manager
from config.settings import get_config, update_config


class SlaveManager:
    """Manages multiple slave device clients."""
    
    def __init__(self, server_url: str = None):
        self.server_url = server_url
        self.clients: Dict[str, SlaveClient] = {}
        self.running = False
        self.logger = get_logger("SlaveManager")
    
    async def start_slaves(self, device_ids: List[str]):
        """Start slave clients for specified devices."""
        self.running = True
        tasks = []
        
        for device_id in device_ids:
            client = SlaveClient(device_id, self.server_url)
            self.clients[device_id] = client
            
            # Start each client in a separate task
            task = asyncio.create_task(client.start())
            tasks.append(task)
        
        # Start status monitoring
        status_task = asyncio.create_task(self._status_monitor())
        tasks.append(status_task)
        
        try:
            # Wait for all tasks
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.error(f"Error in slave manager: {e}")
        finally:
            await self.stop_slaves()
    
    async def stop_slaves(self):
        """Stop all slave clients."""
        self.running = False
        
        for client in self.clients.values():
            await client.stop()
        
        self.logger.info("All slave clients stopped")
    
    async def _status_monitor(self):
        """Monitor and report status of all slave clients."""
        while self.running:
            try:
                await asyncio.sleep(30)  # Report every 30 seconds
                
                total_clients = len(self.clients)
                connected_clients = sum(1 for client in self.clients.values() 
                                      if client.websocket is not None)
                
                print(f"\n📊 SLAVE STATUS: {connected_clients}/{total_clients} devices connected")
                
                for device_id, client in self.clients.items():
                    status = client.get_status()
                    connection_status = "✅ Connected" if status['connected'] else "❌ Disconnected"
                    success_rate = status['success_rate']
                    
                    print(f"  {connection_status} {device_id}: "
                          f"{status['events_executed']}/{status['events_received']} events "
                          f"({success_rate:.1f}% success)")
                
                print()
                
            except Exception as e:
                self.logger.error(f"Error in status monitor: {e}")


def setup_signal_handlers(slave_manager: SlaveManager):
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down slave clients...")
        asyncio.create_task(slave_manager.stop_slaves())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def select_slave_devices(exclude_master: str = None) -> List[str]:
    """Interactive device selection for slave devices."""
    device_manager = get_device_manager()
    
    print("\n" + "="*60)
    print("🤖 SLAVE DEVICES SELECTION")
    print("="*60)
    print("Please select which Android devices will be SLAVE devices.")
    print("Slave devices receive and execute touch events from the master device.")
    print()
    
    # Discover available devices
    devices = await device_manager.discover_devices(force_refresh=True)
    
    # Exclude master device if specified
    if exclude_master and exclude_master in devices:
        devices.remove(exclude_master)
        print(f"ℹ️  Excluding master device: {exclude_master}")
    
    if not devices:
        print("❌ No Android devices available for slave operation!")
        print("   Please ensure you have multiple devices connected.")
        return []
    
    print(f"📱 Found {len(devices)} available device(s) for slave operation:")
    for i, device in enumerate(devices, 1):
        # Get device info for better display
        device_info = await device_manager.get_device_info(device)
        if device_info:
            print(f"   {i}. {device} - {device_info.model} (Android {device_info.android_version})")
        else:
            print(f"   {i}. {device} - (Unable to get device info)")
    
    print("\n💡 Selection options:")
    print("   • 'all' - Use all available devices as slaves")
    print("   • '1,3,5' - Use specific devices by number")
    print("   • 'emulator-5554,emulator-5556' - Use specific device IDs")
    
    while True:
        print(f"\n🔍 Select slave devices:")
        user_input = input("Slave devices: ").strip()
        
        if not user_input:
            print("❌ Please enter your selection")
            continue
        
        if user_input.lower() == 'all':
            print(f"✅ Selected all {len(devices)} devices as slaves")
            return devices
        
        # Parse comma-separated input
        selections = [s.strip() for s in user_input.split(',')]
        selected_devices = []
        
        for selection in selections:
            # Check if it's a number
            try:
                device_index = int(selection) - 1
                if 0 <= device_index < len(devices):
                    selected_devices.append(devices[device_index])
                else:
                    print(f"❌ Invalid device number: {selection}")
                    break
            except ValueError:
                # Check if it's a valid device ID
                if selection in devices:
                    selected_devices.append(selection)
                else:
                    print(f"❌ Invalid device ID: {selection}")
                    break
        else:
            # All selections were valid
            if selected_devices:
                print(f"✅ Selected {len(selected_devices)} slave device(s): {selected_devices}")
                return selected_devices
        
        # Ask if user wants to retry
        retry = input("\n🔄 Try again? (y/n): ").strip().lower()
        if retry not in ['y', 'yes']:
            print("👋 Exiting...")
            return []


async def main():
    """Main function to start the slave clients."""
    parser = argparse.ArgumentParser(
        description='Slave Devices Client for Multi-Device Touch Event Synchronization',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                          # Interactive device selection
  %(prog)s --devices emulator-5554,emulator-5556  # Use specific devices
  %(prog)s --exclude-master emulator-5552  # Exclude master device
  %(prog)s --server ws://*************:8765/slave  # Connect to remote server
  %(prog)s --log-level DEBUG        # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--devices',
        help='Comma-separated list of slave device IDs'
    )
    parser.add_argument(
        '--exclude-master',
        help='Master device ID to exclude from slave selection'
    )
    parser.add_argument(
        '--server',
        help='Server URL (default: from config)'
    )
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default=None,
        help='Logging level (default: from config)'
    )
    
    args = parser.parse_args()
    
    try:
        # Update configuration if command line arguments provided
        config_updates = {}
        if args.log_level:
            config_updates['logging'] = {'level': args.log_level}
        
        if config_updates:
            update_config(**config_updates)
        
        # Initialize logging
        initialize_logging()
        logger = get_logger("SlaveMain")
        
        # Print startup banner
        print_startup_banner()
        
        # Select devices
        if args.devices:
            device_ids = [d.strip() for d in args.devices.split(',')]
        else:
            device_ids = await select_slave_devices(args.exclude_master)
            if not device_ids:
                print("❌ No slave devices selected. Exiting...")
                return
        
        # Validate devices exist
        device_manager = get_device_manager()
        available_devices = await device_manager.discover_devices()
        invalid_devices = [d for d in device_ids if d not in available_devices]
        if invalid_devices:
            print(f"❌ Invalid devices: {invalid_devices}")
            print(f"   Available devices: {available_devices}")
            return
        
        # Create slave manager
        slave_manager = SlaveManager(args.server)
        
        # Setup signal handlers for graceful shutdown
        setup_signal_handlers(slave_manager)
        
        logger.info(f"Starting Slave Clients for devices: {device_ids}")
        print(f"\n🚀 Starting Slave Clients for {len(device_ids)} device(s)")
        print("="*60)
        print("🤖 These devices will receive and execute touch events from the master.")
        print("📱 Events will be automatically scaled for each device's resolution.")
        print("="*60)
        print()
        
        # Start the slave clients
        await slave_manager.start_slaves(device_ids)
        
    except KeyboardInterrupt:
        print("\n🛑 Slave clients stopped by user")
    except Exception as e:
        print(f"\n❌ Slave clients failed to start: {e}")
        logger = get_logger("SlaveMain")
        logger.critical(f"Slave clients startup failed: {e}", exc_info=True)
        sys.exit(1)


def print_startup_banner():
    """Print startup banner with system information."""
    print("\n" + "="*70)
    print("🤖 SLAVE DEVICES CLIENT")
    print("="*70)
    print("This client connects multiple Android devices as slave devices")
    print("that receive and execute touch events from the master device.")
    print()
    print("📋 Slave Client Features:")
    print("  • Multi-device management and coordination")
    print("  • Automatic coordinate scaling for different resolutions")
    print("  • Support for tap, swipe, key, and text input events")
    print("  • Real-time event execution with ADB input commands")
    print("  • Health monitoring and performance statistics")
    print()
    print("🔧 Requirements:")
    print("  • ADB installed and in PATH")
    print("  • Multiple Android devices connected and authorized")
    print("  • USB debugging enabled on all devices")
    print("  • Central server running and accessible")
    print("="*70)
    print()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)
