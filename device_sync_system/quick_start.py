#!/usr/bin/env python3
"""
Quick Start launcher for the Multi-Device Touch Event Synchronization System.

This script provides an interactive menu to easily start different components.
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_banner():
    """Print the application banner."""
    print("\n" + "="*70)
    print("🚀 MULTI-DEVICE TOUCH EVENT SYNCHRONIZATION SYSTEM")
    print("="*70)
    print("Quick Start Launcher - Choose a component to start")
    print("="*70)


def print_menu():
    """Print the main menu."""
    print("\n📋 Available Components:")
    print("  1. 🖥️  Start Central Server")
    print("  2. 🎮 Start Master Client (captures events)")
    print("  3. 🤖 Start Slave Clients (executes events)")
    print("  4. 🔧 Run Setup/Check Requirements")
    print("  5. 📖 View Documentation")
    print("  6. 🔍 Check Connected Devices")
    print("  7. ❌ Exit")


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🚀 {description}")
    print("="*50)
    
    try:
        # Change to the script directory
        script_dir = Path(__file__).parent
        os.chdir(script_dir)
        
        # Run the command
        result = subprocess.run(command, shell=True)
        
        if result.returncode != 0:
            print(f"\n❌ Command failed with return code: {result.returncode}")
        else:
            print(f"\n✅ {description} completed successfully")
            
    except KeyboardInterrupt:
        print(f"\n🛑 {description} interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running {description}: {e}")
    
    input("\nPress Enter to continue...")


def check_devices():
    """Check connected Android devices."""
    print("\n📱 Checking Connected Android Devices")
    print("="*40)
    
    try:
        result = subprocess.run(['adb', 'devices'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("ADB Output:")
            print(result.stdout)
            
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            devices = [line.split()[0] for line in lines if 'device' in line and line.strip()]
            
            if devices:
                print(f"✅ Found {len(devices)} connected device(s)")
                for i, device in enumerate(devices, 1):
                    print(f"   {i}. {device}")
            else:
                print("⚠️  No devices found")
                print("\n💡 Troubleshooting:")
                print("   • Connect Android devices via USB")
                print("   • Enable USB debugging in Developer Options")
                print("   • Accept authorization prompts on devices")
                print("   • Try: adb kill-server && adb start-server")
        else:
            print(f"❌ ADB command failed: {result.stderr}")
            
    except FileNotFoundError:
        print("❌ ADB not found in PATH")
        print("   Please install Android SDK Platform Tools")
    except Exception as e:
        print(f"❌ Error checking devices: {e}")
    
    input("\nPress Enter to continue...")


def view_documentation():
    """Display documentation information."""
    print("\n📖 Documentation")
    print("="*30)
    print("📄 README.md - Comprehensive documentation")
    print("🔧 setup.py - Setup and requirements checker")
    print("📁 examples/ - Example scripts with --help options")
    print()
    print("📋 Quick Reference:")
    print("  • Server: python examples/run_server.py")
    print("  • Master: python examples/run_master.py")
    print("  • Slaves: python examples/run_slaves.py")
    print()
    print("🔍 For detailed help:")
    print("  python examples/run_server.py --help")
    print("  python examples/run_master.py --help")
    print("  python examples/run_slaves.py --help")
    
    input("\nPress Enter to continue...")


def main():
    """Main menu loop."""
    while True:
        print_banner()
        print_menu()
        
        try:
            choice = input("\n🔍 Enter your choice (1-7): ").strip()
            
            if choice == '1':
                run_command("python examples/run_server.py", "Starting Central Server")
                
            elif choice == '2':
                run_command("python examples/run_master.py", "Starting Master Client")
                
            elif choice == '3':
                run_command("python examples/run_slaves.py", "Starting Slave Clients")
                
            elif choice == '4':
                run_command("python setup.py", "Running Setup and Requirements Check")
                
            elif choice == '5':
                view_documentation()
                
            elif choice == '6':
                check_devices()
                
            elif choice == '7':
                print("\n👋 Goodbye!")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1-7.")
                input("Press Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            input("Press Enter to continue...")


if __name__ == "__main__":
    main()
