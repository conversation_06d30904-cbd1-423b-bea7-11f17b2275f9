#!/usr/bin/env python3
"""
Setup script for the Multi-Device Touch Event Synchronization System.

This script helps set up the environment and verify requirements.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_adb():
    """Check if ADB is installed and accessible."""
    try:
        result = subprocess.run(['adb', 'version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ ADB found: {version_line}")
            return True
        else:
            print("❌ ADB command failed")
            return False
    except FileNotFoundError:
        print("❌ ADB not found in PATH")
        print("   Please install Android SDK Platform Tools")
        return False
    except subprocess.TimeoutExpired:
        print("❌ ADB command timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking ADB: {e}")
        return False


def install_dependencies():
    """Install Python dependencies."""
    try:
        print("📦 Installing Python dependencies...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def check_devices():
    """Check for connected Android devices."""
    try:
        result = subprocess.run(['adb', 'devices'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            devices = [line.split()[0] for line in lines if 'device' in line and line.strip()]
            
            if devices:
                print(f"✅ Found {len(devices)} connected device(s):")
                for device in devices:
                    print(f"   • {device}")
                return True
            else:
                print("⚠️  No Android devices found")
                print("   Please connect devices and enable USB debugging")
                return False
        else:
            print("❌ Failed to check devices")
            return False
    except Exception as e:
        print(f"❌ Error checking devices: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    directories = ['logs', 'config']
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"✅ Directory exists: {directory}")


def create_default_config():
    """Create default configuration file if it doesn't exist."""
    config_file = Path('config/device_sync_config.json')
    
    if config_file.exists():
        print("✅ Configuration file exists")
        return
    
    default_config = {
        "server": {
            "host": "0.0.0.0",
            "port": 8765,
            "max_connections": 100,
            "heartbeat_interval": 30.0,
            "connection_timeout": 60.0,
            "message_queue_size": 1000
        },
        "client": {
            "server_url": "ws://127.0.0.1:8765",
            "reconnect_attempts": 5,
            "reconnect_delay": 5.0,
            "heartbeat_interval": 30.0,
            "connection_timeout": 30.0,
            "command_timeout": 10.0
        },
        "coordinates": {
            "enable_scaling": True,
            "fallback_to_raw": True,
            "clamp_to_bounds": True,
            "scaling_precision": 2,
            "coordinate_validation": True
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(levelname)s - %(name)s - %(message)s",
            "file_enabled": True,
            "file_path": "logs/device_sync.log",
            "console_enabled": True,
            "max_file_size": 10485760,
            "backup_count": 5
        },
        "device": {
            "adb_timeout": 10.0,
            "device_discovery_interval": 30.0,
            "health_check_interval": 60.0,
            "max_error_count": 5,
            "input_event_timeout": 5.0
        }
    }
    
    try:
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        print("✅ Created default configuration file")
    except Exception as e:
        print(f"❌ Failed to create config file: {e}")


def make_scripts_executable():
    """Make example scripts executable on Unix systems."""
    if os.name != 'posix':
        return
    
    scripts = [
        'examples/run_server.py',
        'examples/run_master.py',
        'examples/run_slaves.py'
    ]
    
    for script in scripts:
        try:
            os.chmod(script, 0o755)
            print(f"✅ Made executable: {script}")
        except Exception as e:
            print(f"⚠️  Could not make {script} executable: {e}")


def print_usage_instructions():
    """Print usage instructions."""
    print("\n" + "="*60)
    print("🚀 SETUP COMPLETE!")
    print("="*60)
    print("To start using the Multi-Device Touch Event Synchronization System:")
    print()
    print("1. Start the central server:")
    print("   python examples/run_server.py")
    print()
    print("2. In another terminal, start the master client:")
    print("   python examples/run_master.py")
    print()
    print("3. In another terminal, start the slave clients:")
    print("   python examples/run_slaves.py")
    print()
    print("📋 Additional options:")
    print("   • Use --help with any script for detailed options")
    print("   • Use --log-level DEBUG for detailed logging")
    print("   • Check README.md for comprehensive documentation")
    print()
    print("🔧 Troubleshooting:")
    print("   • Ensure ADB is working: adb devices")
    print("   • Check device connections and USB debugging")
    print("   • Review logs in the logs/ directory")
    print("="*60)


def main():
    """Main setup function."""
    print("🔧 Multi-Device Touch Event Synchronization System Setup")
    print("="*60)
    
    success = True
    
    # Check requirements
    if not check_python_version():
        success = False
    
    if not check_adb():
        success = False
        print("\n💡 To install ADB:")
        print("   • Download Android SDK Platform Tools")
        print("   • Add to PATH environment variable")
        print("   • Or install via package manager (apt, brew, etc.)")
    
    if not success:
        print("\n❌ Setup failed due to missing requirements")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Create directories and files
    create_directories()
    create_default_config()
    make_scripts_executable()
    
    # Check devices (optional)
    print("\n📱 Checking for Android devices...")
    check_devices()
    
    # Print usage instructions
    print_usage_instructions()


if __name__ == "__main__":
    main()
