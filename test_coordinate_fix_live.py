#!/usr/bin/env python3
"""
Live coordinate fix test - Test the coordinate mapping fix with real scenarios
"""

def test_coordinate_fix():
    """Test the coordinate fix logic with real data from logs"""
    
    print("🧪 Testing Coordinate Fix Logic")
    print("=" * 60)
    
    # Real data from logs
    master_resolution = {
        "width": 1080,
        "height": 2400,
        "input_x_max": 206695,
        "input_y_max": 206695
    }
    
    farm_resolution = {
        "width": 1440,
        "height": 3120
    }
    
    # Test cases from actual logs
    test_cases = [
        ("Raw coordinates from logs", 25333, 31551),
        ("Center tap (raw)", 103347, 103347),  # Half of input_max
        ("Top-left corner (raw)", 0, 0),
        ("Bottom-right corner (raw)", 206695, 206695),
        ("Quarter point (raw)", 51673, 51673),  # Quarter of input_max
    ]
    
    print(f"📱 Master: {master_resolution['width']}x{master_resolution['height']}")
    print(f"📱 Farm: {farm_resolution['width']}x{farm_resolution['height']}")
    print(f"📏 Master Input Max: {master_resolution['input_x_max']}x{master_resolution['input_y_max']}")
    
    for test_name, raw_x, raw_y in test_cases:
        print(f"\n🎯 Test: {test_name}")
        print(f"   Raw coordinates: ({raw_x}, {raw_y})")
        
        # Step 1: Check if coordinates are raw (larger than screen resolution)
        if raw_x > master_resolution['width'] or raw_y > master_resolution['height']:
            print(f"   🔧 Detected RAW coordinates (larger than screen {master_resolution['width']}x{master_resolution['height']})")
            
            # Convert raw to master screen coordinates
            screen_x = int((raw_x / master_resolution['input_x_max']) * master_resolution['width'])
            screen_y = int((raw_y / master_resolution['input_y_max']) * master_resolution['height'])
            
            # Clamp to screen bounds
            screen_x = max(0, min(screen_x, master_resolution['width']))
            screen_y = max(0, min(screen_y, master_resolution['height']))
            
            print(f"   🔧 Raw->Screen: ({raw_x}, {raw_y}) -> ({screen_x}, {screen_y})")
        else:
            print(f"   ✅ Already screen coordinates")
            screen_x, screen_y = raw_x, raw_y
        
        # Step 2: Scale from master screen to farm screen
        normalized_x = screen_x / master_resolution['width']
        normalized_y = screen_y / master_resolution['height']
        
        farm_x = int(normalized_x * farm_resolution['width'])
        farm_y = int(normalized_y * farm_resolution['height'])
        
        # Clamp to farm bounds
        farm_x = max(0, min(farm_x, farm_resolution['width']))
        farm_y = max(0, min(farm_y, farm_resolution['height']))
        
        print(f"   📐 Screen->Farm: ({screen_x}, {screen_y}) -> ({farm_x}, {farm_y})")
        
        # Calculate expected position as percentage
        expected_x_pct = (raw_x / master_resolution['input_x_max']) * 100
        expected_y_pct = (raw_y / master_resolution['input_y_max']) * 100
        actual_x_pct = (farm_x / farm_resolution['width']) * 100
        actual_y_pct = (farm_y / farm_resolution['height']) * 100
        
        print(f"   📊 Expected position: {expected_x_pct:.1f}%, {expected_y_pct:.1f}%")
        print(f"   📊 Actual position: {actual_x_pct:.1f}%, {actual_y_pct:.1f}%")
        
        # Check if the mapping is correct
        x_diff = abs(expected_x_pct - actual_x_pct)
        y_diff = abs(expected_y_pct - actual_y_pct)
        
        if x_diff < 1.0 and y_diff < 1.0:
            print(f"   ✅ PASS: Position mapping is accurate")
        else:
            print(f"   ❌ FAIL: Position mapping is off by {x_diff:.1f}%, {y_diff:.1f}%")

def test_old_vs_new_logic():
    """Compare old vs new coordinate scaling logic"""
    
    print(f"\n🔄 Comparing Old vs New Logic")
    print("=" * 60)
    
    # Test data from logs
    master_width = 1080
    master_height = 2400
    farm_width = 1440
    farm_height = 3120
    
    # Raw coordinates from logs
    raw_x, raw_y = 25333, 31551
    
    print(f"📱 Input: Raw coordinates ({raw_x}, {raw_y})")
    print(f"📱 Master: {master_width}x{master_height}")
    print(f"📱 Farm: {farm_width}x{farm_height}")
    
    # OLD LOGIC (treating raw as screen coordinates)
    print(f"\n❌ OLD LOGIC (incorrect):")
    old_normalized_x = raw_x / master_width
    old_normalized_y = raw_y / master_height
    old_farm_x = int(old_normalized_x * farm_width)
    old_farm_y = int(old_normalized_y * farm_height)
    print(f"   Treating raw as screen: ({raw_x}, {raw_y}) -> ({old_farm_x}, {old_farm_y})")
    print(f"   Result: WAY outside farm bounds ({farm_width}x{farm_height})")
    
    # NEW LOGIC (convert raw to screen first)
    print(f"\n✅ NEW LOGIC (correct):")
    input_x_max = 206695
    input_y_max = 206695
    
    # Convert raw to screen
    screen_x = int((raw_x / input_x_max) * master_width)
    screen_y = int((raw_y / input_y_max) * master_height)
    print(f"   Raw->Screen: ({raw_x}, {raw_y}) -> ({screen_x}, {screen_y})")
    
    # Scale screen to farm
    new_normalized_x = screen_x / master_width
    new_normalized_y = screen_y / master_height
    new_farm_x = int(new_normalized_x * farm_width)
    new_farm_y = int(new_normalized_y * farm_height)
    print(f"   Screen->Farm: ({screen_x}, {screen_y}) -> ({new_farm_x}, {new_farm_y})")
    print(f"   Result: Within farm bounds ✅")

if __name__ == "__main__":
    test_coordinate_fix()
    test_old_vs_new_logic()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   The fix detects when raw coordinates are being sent instead of screen coordinates")
    print(f"   It converts raw->screen first, then screen->farm")
    print(f"   This should resolve the coordinate mapping issue!")
