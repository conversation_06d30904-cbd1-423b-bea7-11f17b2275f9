import unittest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
import sys
import os

import websockets
from websockets import ConnectionClosedOK

# Import the functions and global variables from central-device.py
# We need to be careful with global state in tests, so we'll reset it or mock it.
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src import central_device
# All references will be central_device.variable or central_device.function

class TestCentralDevice(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Reset global state before each test using patch.object for isolation
        self.patcher_farm_devices = patch.object(central_device, 'farm_devices', new={})
        self.patcher_farm_device_info = patch.object(central_device, 'farm_device_info', new={})
        self.patcher_master_device_websocket = patch.object(central_device, 'master_device_websocket', new=None)
        self.patcher_master_device_id = patch.object(central_device, 'master_device_id', new=None)
        self.patcher_master_device_info = patch.object(central_device, 'master_device_info', new=None)
        self.patcher_master_resolution = patch.object(central_device, 'master_resolution', new=None)

        self.patcher_farm_devices.start()
        self.patcher_farm_device_info.start()
        self.patcher_master_device_websocket.start()
        self.patcher_master_device_id.start()
        self.patcher_master_device_info.start()
        self.patcher_master_resolution.start()

    def tearDown(self):
        self.patcher_farm_devices.stop()
        self.patcher_farm_device_info.stop()
        self.patcher_master_device_websocket.stop()
        self.patcher_master_device_id.stop()
        self.patcher_master_device_info.stop()
        self.patcher_master_resolution.stop()

    async def test_verify_server_is_connected(self):
        # Description: Verify that the server is considered connected when master_device_websocket is set.
        # Functionality: handle_master_device
        # Expected Outcome: master_device_websocket is not None after a master connection.
        mock_websocket = AsyncMock()
        mock_websocket.remote_address = ("127.0.0.1", 12345)
        master_info_message = json.dumps({
            "action": "device_info",
            "role": "master",
            "device_info": {
                "id": "master_test",
                "model": "Test",
                "android_version": "10",
                "screen_width": 100,
                "screen_height": 200,
                "master_input_x_max": 100,
                "master_input_y_max": 200
            }
        })
        # Simulate the websocket receiving the device_info message and then awaiting indefinitely
        # to keep the task alive until explicitly cancelled by addCleanup.
        mock_websocket.recv.side_effect = [master_info_message, asyncio.Future()]

        with patch('src.central_device.broadcast_to_farm_devices', new_callable=AsyncMock):
            task = asyncio.create_task(central_device.handle_master_device(mock_websocket))

            await asyncio.sleep(0.01) # Allow event loop to process the message and set globals
            
            self.assertIsNotNone(central_device.master_device_websocket)
            self.assertEqual(central_device.master_device_websocket, mock_websocket)
            self.assertEqual(central_device.master_device_id, "master_test")
            self.assertIsNotNone(central_device.master_device_info)

            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass


    async def test_verify_master_device_is_only_one_device(self):
        # Description: Verify that only one master device can be connected at a time.
        # Functionality: handle_master_device
        # Expected Outcome: The latest master connection replaces any previous one.
        mock_websocket_master1 = AsyncMock()
        mock_websocket_master1.remote_address = ("127.0.0.1", 10001)
        master_info_message1 = json.dumps({
            "action": "device_info",
            "role": "master",
            "device_info": {
                "id": "master_one",
                "model": "M1",
                "android_version": "11",
                "screen_width": 1080,
                "screen_height": 1920,
                "master_input_x_max": 1200,
                "master_input_y_max": 2400
            }
        })
        # Simulate master1 sending info and then awaiting indefinitely
        mock_websocket_master1.recv.side_effect = [master_info_message1, asyncio.Future()]


        mock_websocket_master2 = AsyncMock()
        mock_websocket_master2.remote_address = ("127.0.0.1", 10002)
        master_info_message2 = json.dumps({
            "action": "device_info",
            "role": "master",
            "device_info": {
                "id": "master_two",
                "model": "M2",
                "android_version": "12",
                "screen_width": 720,
                "screen_height": 1280,
                "master_input_x_max": 800,
                "master_input_y_max": 1300
            }
        })
        # Simulate master2 sending info and then awaiting indefinitely
        mock_websocket_master2.recv.side_effect = [master_info_message2, asyncio.Future()]


        with patch('src.central_device.broadcast_to_farm_devices', new_callable=AsyncMock):
            # Connect first master
            task1 = asyncio.create_task(central_device.handle_master_device(mock_websocket_master1))
            await asyncio.sleep(0.1) # Give time for processing

            self.assertEqual(central_device.master_device_id, "master_one")
            self.assertEqual(central_device.master_device_websocket, mock_websocket_master1)

            # Connect second master
            task2 = asyncio.create_task(central_device.handle_master_device(mock_websocket_master2))
            await asyncio.sleep(0.1) # Give time for processing

            # Verify that the second master replaced the first
            self.assertEqual(central_device.master_device_id, "master_two")
            self.assertEqual(central_device.master_device_websocket, mock_websocket_master2)

            # Explicitly cancel tasks after assertions
            task1.cancel()
            task2.cancel()
            try:
                await asyncio.gather(task1, task2)
            except asyncio.CancelledError:
                pass


    async def test_verify_slave_device_id_not_master_device(self):
        # Description: Verify that a farm device cannot have the same ID as the master device.
        # Functionality: handle_farm_device
        # Expected Outcome: The farm device connection is closed if its ID matches the master's.
        mock_websocket = AsyncMock()
        mock_websocket.remote_address = ("127.0.0.1", 54321)
        farm_info_message = json.dumps({
            "action": "device_info",
            "role": "farm",
            "device_info": {
                "id": "master_emulator", # This ID matches the master_device_id set below
                "model": "Galaxy S20",
                "android_version": "10",
                "screen_width": 1080,
                "screen_height": 2400
            }
        })
        mock_websocket.recv.side_effect = [farm_info_message, websockets.exceptions.ConnectionClosedOK(1000, "Test close")]

        central_device.master_device_id = "master_emulator"
        central_device.farm_devices = {}

        with self.assertLogs('CentralServer', level='INFO') as cm:
            task = asyncio.create_task(central_device.handle_farm_device(mock_websocket))
            await asyncio.sleep(0.1) # Give time for processing and log emission
            await task # Wait for the task to complete

            # Check if the expected log message is present anywhere in the output
            found_log = False
            for log_entry in cm.output:
                if "Farm device master_emulator is actually the master device. Not adding to farm_devices." in log_entry:
                    found_log = True
                    break
            self.assertTrue(found_log, "Expected log message not found in output.")
        
        mock_websocket.close.assert_called_once_with(1000, "Master device attempting to connect as farm device.")
        self.assertNotIn("master_emulator", central_device.farm_devices)

    async def test_verify_master_not_connected_as_slave(self):
        # Description: Verify that if a master device was somehow added to farm_devices, it's removed when it connects as master.
        # Functionality: handle_master_device
        # Expected Outcome: Master device is removed from farm_devices if present.
        mock_websocket_master = AsyncMock()
        mock_websocket_master.remote_address = ("127.0.0.1", 12345)
        master_info_message = json.dumps({
            "action": "device_info",
            "role": "master",
            "device_info": {
                "id": "master_emulator",
                "model": "Pixel 4",
                "android_version": "11",
                "screen_width": 1080,
                "screen_height": 1920,
                "master_input_x_max": 1200,
                "master_input_y_max": 2400
            }
        })
        mock_websocket_master.recv.side_effect = [master_info_message, asyncio.Future()]

        # Simulate master device being incorrectly in farm_devices
        central_device.farm_devices["master_emulator"] = AsyncMock()
        central_device.farm_device_info["master_emulator"] = {"id": "master_emulator", "role": "farm"}

        with patch('src.central_device.broadcast_to_farm_devices', new_callable=AsyncMock) as mock_broadcast:
            with self.assertLogs('CentralServer', level='WARNING') as cm:
                task = asyncio.create_task(central_device.handle_master_device(mock_websocket_master))
                await asyncio.sleep(0.01) # Allow event loop to process the message and set globals

                # Check if the expected log message is present anywhere in the output
                found_log = False
                for log_entry in cm.output:
                    if "Master device master_emulator was incorrectly in farm_devices, removed." in log_entry:
                        found_log = True
                        break
                self.assertTrue(found_log, "Expected log message not found in output.")
            
            self.assertNotIn("master_emulator", central_device.farm_devices)
            self.assertNotIn("master_emulator", central_device.farm_device_info)
            self.assertEqual(central_device.master_device_id, "master_emulator")
            self.assertIsNotNone(central_device.master_device_websocket) # Should be set at this point
            mock_broadcast.assert_called_once() # Should still broadcast resolution update

            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

if __name__ == '__main__':
    unittest.main()