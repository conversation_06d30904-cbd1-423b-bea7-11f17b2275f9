import unittest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
import sys
import os
import websockets

# Import functions from central-device-router.py
from src.central_device_router import (
    broadcast_to_farm_devices,
    handle_master_device,
    handle_farm_device,
    farm_devices,
    farm_device_info,
    master_device_websocket,
    master_device_id,
    master_device_info,
    logger
)


class TestCentralDeviceRouter(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Clear global states before each test
        farm_devices.clear()
        farm_device_info.clear()
        global master_device_websocket, master_device_id, master_device_info
        master_device_websocket = None
        master_device_id = None
        master_device_info = None
        
        # Mock logger to prevent actual logging during tests
        self.mock_logger_info = patch('central_device_router.logger.info').start()
        self.mock_logger_warning = patch('central_device_router.logger.warning').start()
        self.mock_logger_error = patch('central_device_router.logger.error').start()
        self.mock_logger_debug = patch('central_device_router.logger.debug').start()

    def tearDown(self):
        patch.stopall()

    async def test_broadcast_to_farm_devices_happy_path(self):
        """
        Test Case: Happy path for broadcasting messages to farm devices.
        Functionality Validated: Ensures messages are sent to all connected farm devices successfully.
        Expected Outcomes:
            - Each farm device's websocket.send is called once with the message.
            - No devices are removed from farm_devices or farm_device_info.
            - logger.info is called with a success message.
        """
        mock_ws1 = AsyncMock()
        mock_ws2 = AsyncMock()
        farm_devices["device1"] = mock_ws1
        farm_devices["device2"] = mock_ws2
        farm_device_info["device1"] = {"id": "device1"}
        farm_device_info["device2"] = {"id": "device2"}

        message = json.dumps({"action": "click", "x": 100, "y": 200})
        await broadcast_to_farm_devices(message)

        mock_ws1.send.assert_called_once_with(message)
        mock_ws2.send.assert_called_once_with(message)
        self.assertEqual(len(farm_devices), 2)
        self.assertEqual(len(farm_device_info), 2)
        self.mock_logger_info.assert_any_call("Successfully broadcasted click to 2 farm device(s)")

    async def test_broadcast_to_farm_devices_with_disconnection(self):
        """
        Test Case: Broadcasting with a disconnected farm device.
        Functionality Validated: Ensures disconnected devices are properly removed.
        Expected Outcomes:
            - The disconnected device's websocket.send raises ConnectionClosed.
            - The disconnected device is removed from farm_devices and farm_device_info.
            - logger.warning is called for the disconnected device.
            - logger.info is called with a success message for remaining devices.
        """
        mock_ws1 = AsyncMock()
        mock_ws2 = AsyncMock()
        mock_ws2.send.side_effect = websockets.exceptions.ConnectionClosed(code=1000, reason="test")

        farm_devices["device1"] = mock_ws1
        farm_devices["device2"] = mock_ws2
        farm_device_info["device1"] = {"id": "device1"}
        farm_device_info["device2"] = {"id": "device2"}

        message = json.dumps({"action": "swipe"})
        await broadcast_to_farm_devices(message)

        mock_ws1.send.assert_called_once_with(message)
        mock_ws2.send.assert_called_once_with(message) # Still called, but raises exception
        self.assertNotIn("device2", farm_devices)
        self.assertNotIn("device2", farm_device_info)
        self.assertEqual(len(farm_devices), 1)
        self.assertEqual(len(farm_device_info), 1)
        self.mock_logger_warning.assert_any_call("Connection closed while sending to farm device: device2")
        self.mock_logger_info.assert_any_call("Removed disconnected farm device: device2")
        self.mock_logger_info.assert_any_call("Successfully broadcasted swipe to 1 farm device(s)")

    async def test_broadcast_to_farm_devices_no_devices(self):
        """
        Test Case: Broadcasting when no farm devices are connected.
        Functionality Validated: Ensures appropriate logging when no devices are present.
        Expected Outcomes:
            - logger.warning is called indicating no farm devices are connected.
        """
        message = json.dumps({"action": "input_text"})
        await broadcast_to_farm_devices(message)

        self.mock_logger_warning.assert_called_once_with("No farm devices connected. Action not replicated.")
        self.assertEqual(len(farm_devices), 0)
        self.assertEqual(len(farm_device_info), 0)

    async def test_handle_master_device_connect_and_disconnect(self):
        """
        Test Case: Master device connection and disconnection.
        Functionality Validated: Ensures master device websocket and info are correctly set and cleared.
        Expected Outcomes:
            - master_device_websocket, master_device_id, master_device_info are set on connect.
            - master_device_websocket, master_device_id, master_device_info are cleared on disconnect.
            - logger.info is called for connection and disconnection.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 12345)
        mock_ws.__aiter__.return_value = [] # Simulate immediate disconnection

        await handle_master_device(mock_ws)

        self.assertIsNone(master_device_websocket)
        self.assertIsNone(master_device_id)
        self.assertIsNone(master_device_info)
        self.mock_logger_info.assert_any_call("Master device connected: ('127.0.0.1', 12345)")
        self.mock_logger_info.assert_any_call("Master device disconnected: ('127.0.0.1', 12345)")

    async def test_handle_master_device_device_info_message(self):
        """
        Test Case: Master device sends device_info message.
        Functionality Validated: Ensures master device info is correctly stored and not broadcasted.
        Expected Outcomes:
            - master_device_info and master_device_id are updated.
            - broadcast_to_farm_devices is NOT called.
            - logger.info is called with master device info.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 12345)
        
        device_info_msg = json.dumps({
            "action": "device_info",
            "role": "master",
            "device_info": {"id": "master_id", "model": "Pixel", "android_version": "13"}
        })
        mock_ws.__aiter__.return_value = [device_info_msg, json.dumps({"action": "disconnect"})] # Add another message to allow loop to continue

        with patch('central_device_router.broadcast_to_farm_devices', new_callable=AsyncMock) as mock_broadcast:
            # Run handle_master_device in a task and cancel it after processing the device_info message
            task = asyncio.create_task(handle_master_device(mock_ws))
            await asyncio.sleep(0.1) # Give it a moment to process the first message
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

            self.assertEqual(master_device_id, "master_id")
            self.assertEqual(master_device_info, {"id": "master_id", "model": "Pixel", "android_version": "13"})
            mock_broadcast.assert_not_called()
            self.mock_logger_info.assert_any_call("Master device info: Pixel (Android 13, ID: master_id)")

    async def test_handle_master_device_action_message(self):
        """
        Test Case: Master device sends an action message.
        Functionality Validated: Ensures action messages are broadcasted to farm devices.
        Expected Outcomes:
            - broadcast_to_farm_devices is called with the action message.
            - logger.info is called for receiving action from master.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 12345)
        action_msg = json.dumps({"action": "tap", "x": 50, "y": 50})
        mock_ws.__aiter__.return_value = [action_msg, json.dumps({"action": "disconnect"})]

        with patch('central_device_router.broadcast_to_farm_devices', new_callable=AsyncMock) as mock_broadcast:
            task = asyncio.create_task(handle_master_device(mock_ws))
            await asyncio.sleep(0.1)
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

            mock_broadcast.assert_called_once_with(action_msg)
            self.mock_logger_info.assert_any_call(f"Received action from master: {json.loads(action_msg)}")

    async def test_handle_master_device_invalid_json(self):
        """
        Test Case: Master device sends invalid JSON.
        Functionality Validated: Ensures invalid JSON messages are handled and logged as errors.
        Expected Outcomes:
            - logger.error is called for invalid JSON.
            - broadcast_to_farm_devices is NOT called.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 12345)
        invalid_msg = "this is not json"
        mock_ws.__aiter__.return_value = [invalid_msg, json.dumps({"action": "disconnect"})]

        with patch('central_device_router.broadcast_to_farm_devices', new_callable=AsyncMock) as mock_broadcast:
            task = asyncio.create_task(handle_master_device(mock_ws))
            await asyncio.sleep(0.1)
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

            self.mock_logger_error.assert_any_call(f"Received invalid JSON from master: {invalid_msg}")
            mock_broadcast.assert_not_called()

    async def test_handle_farm_device_connect_and_disconnect(self):
        """
        Test Case: Farm device connection and disconnection.
        Functionality Validated: Ensures farm device is added and removed from global states.
        Expected Outcomes:
            - logger.info is called for connection and disconnection.
            - Farm device is added to farm_devices and farm_device_info on connect (after device_info message).
            - Farm device is removed from farm_devices and farm_device_info on disconnect.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 54321)
        
        device_info_msg = json.dumps({
            "action": "device_info",
            "role": "farm",
            "device_info": {"id": "farm_device_1", "model": "Emulator", "android_version": "10"}
        })
        mock_ws.__aiter__.return_value = [device_info_msg, json.dumps({"action": "disconnect"})] # Simulate a message then disconnection

        task = asyncio.create_task(handle_farm_device(mock_ws))
        await asyncio.sleep(0.1) # Allow time for connection and first message processing
        self.assertIn("farm_device_1", farm_devices)
        self.assertIn("farm_device_1", farm_device_info)
        self.mock_logger_info.assert_any_call("Farm device connected: ('127.0.0.1', 54321)")
        self.mock_logger_info.assert_any_call("Farm device info: Emulator (Android 10, ID: farm_device_1)")

        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass
        
        self.assertNotIn("farm_device_1", farm_devices)
        self.assertNotIn("farm_device_1", farm_device_info)
        self.mock_logger_info.assert_any_call("Farm device disconnected: farm_device_1")

    async def test_handle_farm_device_master_connecting_as_farm(self):
        """
        Test Case: Master device attempts to connect as a farm device.
        Functionality Validated: Ensures such connections are rejected and closed.
        Expected Outcomes:
            - The websocket connection is closed with a specific code and reason.
            - The master device is not added to farm_devices or farm_device_info.
            - logger.info is called.
        """
        global master_device_id
        master_device_id = "master_id_actual" # Set a master ID

        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 54321)
        
        device_info_msg = json.dumps({
            "action": "device_info",
            "role": "farm",
            "device_info": {"id": "master_id_actual", "model": "MasterPhone", "android_version": "14"}
        })
        mock_ws.__aiter__.return_value = [device_info_msg]

        await handle_farm_device(mock_ws)

        mock_ws.close.assert_called_once_with(1000, "Master device attempting to connect as farm device.")
        self.assertNotIn("master_id_actual", farm_devices)
        self.assertNotIn("master_id_actual", farm_device_info)
        self.mock_logger_info.assert_any_call("Farm device master_id_actual is actually the master device. Not adding to farm_devices.")

    async def test_handle_farm_device_unexpected_message(self):
        """
        Test Case: Farm device sends an unexpected message (not device_info).
        Functionality Validated: Ensures such messages are logged as warnings.
        Expected Outcomes:
            - logger.warning is called.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 54321)
        
        unexpected_msg = json.dumps({"action": "heartbeat", "status": "ok"})
        device_info_msg = json.dumps({
            "action": "device_info",
            "role": "farm",
            "device_info": {"id": "farm_device_2", "model": "Tablet", "android_version": "9"}
        })
        mock_ws.__aiter__.return_value = [device_info_msg, unexpected_msg, json.dumps({"action": "disconnect"})]

        task = asyncio.create_task(handle_farm_device(mock_ws))
        await asyncio.sleep(0.1)
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        self.mock_logger_warning.assert_any_call(f"Received unexpected message from farm device farm_device_2: {json.loads(unexpected_msg)}")

    async def test_handle_farm_device_invalid_json(self):
        """
        Test Case: Farm device sends invalid JSON.
        Functionality Validated: Ensures invalid JSON messages are handled and logged as errors.
        Expected Outcomes:
            - logger.error is called for invalid JSON.
        """
        mock_ws = AsyncMock()
        mock_ws.remote_address = ("127.0.0.1", 54321)
        
        device_info_msg = json.dumps({
            "action": "device_info",
            "role": "farm",
            "device_info": {"id": "farm_device_3", "model": "Phone", "android_version": "11"}
        })
        invalid_msg = "not a json string"
        mock_ws.__aiter__.return_value = [device_info_msg, invalid_msg, json.dumps({"action": "disconnect"})]

        task = asyncio.create_task(handle_farm_device(mock_ws))
        await asyncio.sleep(0.1)
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        self.mock_logger_error.assert_any_call(f"Received invalid JSON from farm device farm_device_3: {invalid_msg}")

if __name__ == '__main__':
    unittest.main()