import unittest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
import sys
import os
import websockets

# Import functions from farm-device.py
from src.farm_device import (
    get_device_info,
    handle_messages,
    logger
)

class TestFarmDevice(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Mock logger to prevent actual logging during tests
        self.mock_logger_info = patch('farm_device.logger.info').start()
        self.mock_logger_warning = patch('farm_device.logger.warning').start()
        self.mock_logger_error = patch('farm_device.logger.error').start()
        self.mock_logger_debug = patch('farm_device.logger.debug').start()

    def tearDown(self):
        patch.stopall()

    @patch('farm_device.asyncio.create_subprocess_exec')
    async def test_get_device_info_happy_path(self, mock_create_subprocess_exec):
        """
        Test Case: Happy path for getting device information.
        Functionality Validated: Ensures correct device model and Android version are returned.
        Expected Outcomes:
            - Returns a dictionary with 'id', 'model', and 'android_version'.
            - Subprocess calls are made with correct adb commands.
        """
        mock_process_model = AsyncMock()
        mock_process_model.communicate.return_value = (b"Pixel 3a\n", b"")
        mock_process_version = AsyncMock()
        mock_process_version.communicate.return_value = (b"12\n", b"")

        mock_create_subprocess_exec.side_effect = [mock_process_model, mock_process_version]

        device_id = "emulator-5554"
        info = await get_device_info(device_id)

        self.assertEqual(info, {
            "id": "emulator-5554",
            "model": "Pixel 3a",
            "android_version": "12"
        })
        mock_create_subprocess_exec.assert_any_call(
            'adb', '-s', device_id, 'shell', 'getprop', 'ro.product.model',
            stdout=asyncio.subprocess.PIPE
        )
        mock_create_subprocess_exec.assert_any_call(
            'adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.release',
            stdout=asyncio.subprocess.PIPE
        )
        self.mock_logger_error.assert_not_called()

    @patch('farm_device.asyncio.create_subprocess_exec')
    async def test_get_device_info_error_handling(self, mock_create_subprocess_exec):
        """
        Test Case: Error handling when getting device information fails.
        Functionality Validated: Ensures errors during subprocess execution are caught and logged.
        Expected Outcomes:
            - Returns a dictionary with 'Unknown' values for model and version.
            - logger.error is called.
        """
        mock_create_subprocess_exec.side_effect = Exception("ADB command failed")

        device_id = "emulator-5554"
        info = await get_device_info(device_id)

        self.assertEqual(info, {
            "id": "emulator-5554",
            "model": "Unknown",
            "android_version": "Unknown"
        })
        self.mock_logger_error.assert_called_once_with("Error getting device info: ADB command failed")

    @patch('farm_device.get_device_id', new_callable=AsyncMock)
    @patch('farm_device.get_device_info', new_callable=AsyncMock)
    async def test_handle_messages_happy_path(self, mock_get_device_info, mock_get_device_id):
        """
        Test Case: Happy path for handling incoming messages.
        Functionality Validated: Ensures device info is sent to the central server.
        Expected Outcomes:
            - websocket.send is called with the device_info message.
            - logger.info is called with device information and listening status.
        """
        mock_ws = AsyncMock()
        mock_ws.__aiter__.return_value = [] # Simulate no further messages after initial setup

        mock_get_device_id.return_value = "emulator-5554"
        mock_get_device_info.return_value = {
            "id": "emulator-5554",
            "model": "Pixel 3a",
            "android_version": "12"
        }

        await handle_messages(mock_ws)

        expected_message = json.dumps({
            "action": "device_info",
            "role": "farm",
            "device_info": {
                "id": "emulator-5554",
                "model": "Pixel 3a",
                "android_version": "12"
            }
        })
        mock_ws.send.assert_called_once_with(expected_message)
        self.mock_logger_info.assert_any_call("Farm device: Pixel 3a (Android 12, ID: emulator-5554)")
        self.mock_logger_info.assert_any_call("Listening for actions to execute on device: emulator-5554")
        self.mock_logger_error.assert_not_called()

    @patch('farm_device.get_device_id', new_callable=AsyncMock)
    @patch('farm_device.get_device_info', new_callable=AsyncMock)
    async def test_handle_messages_no_device_id(self, mock_get_device_info, mock_get_device_id):
        """
        Test Case: Handling messages when no device ID is available.
        Functionality Validated: Ensures the function exits early and logs an error.
        Expected Outcomes:
            - websocket.send is NOT called.
            - logger.error is called indicating no device ID.
        """
        mock_ws = AsyncMock()
        mock_get_device_id.return_value = None

        await handle_messages(mock_ws)

        mock_ws.send.assert_not_called()
        mock_get_device_info.assert_not_called()
        self.mock_logger_error.assert_called_once_with("No device ID available. Cannot execute actions.")

    @patch('farm_device.get_device_id', new_callable=AsyncMock)
    @patch('farm_device.get_device_info', new_callable=AsyncMock)
    async def test_handle_messages_send_device_info_error(self, mock_get_device_info, mock_get_device_id):
        """
        Test Case: Error sending device info to central server.
        Functionality Validated: Ensures errors during websocket.send are caught and logged.
        Expected Outcomes:
            - logger.error is called for the send error.
        """
        mock_ws = AsyncMock()
        mock_ws.send.side_effect = websockets.exceptions.ConnectionClosed(code=1000, reason="test")

        mock_get_device_id.return_value = "emulator-5554"
        mock_get_device_info.return_value = {
            "id": "emulator-5554",
            "model": "Pixel 3a",
            "android_version": "12"
        }

        await handle_messages(mock_ws)

        self.mock_logger_error.assert_called_once_with(
            "Error sending device info: ConnectionClosed(1000, 'test')"
        )
        self.mock_logger_info.assert_any_call("Farm device: Pixel 3a (Android 12, ID: emulator-5554)")
        self.mock_logger_info.assert_any_call("Listening for actions to execute on device: emulator-5554")

if __name__ == '__main__':
    unittest.main()