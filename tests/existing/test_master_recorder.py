import unittest
import asyncio
import json
import sys
import os
from unittest.mock import AsyncMock, patch, MagicMock, call

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import websockets

from src import master_recorder
from src.master_recorder import (
    connect_websocket,
    send_action,
    get_available_devices,
    prompt_for_master_device,
    validate_device_connection,
    get_screen_resolution,
    get_device_info,
    get_input_device_capabilities,
    monitor_adb_events,
    main,
    current_touch_event
)

class TestMasterRecorder(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # Reset global state before each test
        master_recorder.current_touch_event = {"x": None, "y": None, "down": False}

    @patch('src.master_recorder.websockets.connect', new_callable=AsyncMock)
    async def test_connect_websocket_success(self, mock_websocket_connect):
        # Description: Verify successful WebSocket connection.
        # Functionality: connect_websocket
        # Expected Outcome: Returns a websocket object.
        mock_ws = AsyncMock()
        mock_websocket_connect.return_value = mock_ws
        
        websocket = await connect_websocket()
        self.assertEqual(websocket, mock_ws)
        mock_websocket_connect.assert_called_once_with(master_recorder.CENTRAL_SERVER_URL)

    @patch('src.master_recorder.websockets.connect', new_callable=AsyncMock)
    async def test_connect_websocket_retry_logic(self, mock_websocket_connect):
        # Description: Verify retry mechanism for WebSocket connection.
        # Functionality: connect_websocket
        # Expected Outcome: Attempts connection multiple times with delays.
        mock_websocket_connect.side_effect = [
            ConnectionRefusedError,
            websockets.exceptions.ConnectionClosed,
            AsyncMock() # Third attempt succeeds
        ]
        
        with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
            websocket = await connect_websocket()
            self.assertIsNotNone(websocket)
            self.assertEqual(mock_websocket_connect.call_count, 3)
            self.assertEqual(mock_sleep.call_count, 2) # Two retries mean two sleeps

    async def test_send_action_success(self):
        # Description: Verify successful sending of an action message.
        # Functionality: send_action
        # Expected Outcome: Websocket.send is called with JSON message, returns True.
        mock_websocket = AsyncMock()
        action_data = {"action": "tap", "x": 100, "y": 200}
        
        result = await send_action(mock_websocket, action_data)
        self.assertTrue(result)
        mock_websocket.send.assert_called_once_with(json.dumps(action_data))


    @patch('src.master_recorder.asyncio.create_subprocess_exec')
    async def test_get_available_devices_happy_path(self, mock_subprocess_exec):
        # Description: Verify successful retrieval of available ADB devices.
        # Functionality: get_available_devices
        # Expected Outcome: Returns a list of device IDs.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (
            b"List of devices attached\nemulator-5554    device\nemulator-5556    device\n",
            b""
        )
        mock_subprocess_exec.return_value = mock_process

        devices = await get_available_devices()
        self.assertEqual(devices, ["emulator-5554", "emulator-5556"])
        mock_subprocess_exec.assert_called_once_with('adb', 'devices', '-l', stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)

    @patch('src.master_recorder.asyncio.create_subprocess_exec')
    async def test_validate_device_connection_success(self, mock_subprocess_exec):
        # Description: Verify successful validation of device connection.
        # Functionality: validate_device_connection
        # Expected Outcome: Returns True.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (b"connection_test\n", b"")
        mock_subprocess_exec.return_value = mock_process

        result = await validate_device_connection("emulator-5554")
        self.assertTrue(result)
        mock_subprocess_exec.assert_called_once_with(
            'adb', '-s', 'emulator-5554', 'shell', 'echo', 'connection_test',
            stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
        )

    @patch('src.master_recorder.asyncio.create_subprocess_exec')
    async def test_validate_device_connection_failure(self, mock_subprocess_exec):
        # Description: Verify handling of failed device connection validation.
        # Functionality: validate_device_connection
        # Expected Outcome: Returns False, logs error.
        mock_process = AsyncMock()
        mock_process.returncode = 1
        mock_process.communicate.return_value = (b"", b"error: device not found\n")
        mock_subprocess_exec.return_value = mock_process

        with self.assertLogs('MasterRecorder', level='ERROR') as cm:
            result = await validate_device_connection("non_existent_device")
            self.assertFalse(result)
            self.assertIn("Cannot connect to device non_existent_device: error: device not found", cm.output[0])

    @patch('src.master_recorder.asyncio.create_subprocess_exec')
    async def test_get_screen_resolution_happy_path(self, mock_subprocess_exec):
        # Description: Verify successful retrieval of screen resolution.
        # Functionality: get_screen_resolution
        # Expected Outcome: Returns a dictionary with width and height.
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b"Physical size: 1080x1920\n", b"")
        mock_subprocess_exec.return_value = mock_process

        resolution = await get_screen_resolution("emulator-5554")
        self.assertEqual(resolution, {"width": 1080, "height": 1920})

    @patch('src.master_recorder.get_screen_resolution', new_callable=AsyncMock)
    @patch('src.master_recorder.asyncio.create_subprocess_exec')
    async def test_get_device_info_happy_path(self, mock_subprocess_exec, mock_get_screen_resolution):
        # Description: Verify successful retrieval of comprehensive device information.
        # Functionality: get_device_info
        # Expected Outcome: Returns a dictionary with device ID, model, Android version, and screen resolution.
        mock_get_screen_resolution.return_value = {"width": 1080, "height": 1920}

        mock_model_process = AsyncMock()
        mock_model_process.communicate.return_value = (b"Pixel 4\n", b"")
        mock_version_process = AsyncMock()
        mock_version_process.communicate.return_value = (b"11\n", b"")

        mock_subprocess_exec.side_effect = [mock_model_process, mock_version_process]

        device_info = await get_device_info("emulator-5554")
        self.assertIsNotNone(device_info)
        self.assertEqual(device_info["id"], "emulator-5554")
        self.assertEqual(device_info["model"], "Pixel 4")
        self.assertEqual(device_info["android_version"], "11")
        self.assertEqual(device_info["screen_width"], 1080)
        self.assertEqual(device_info["screen_height"], 1920)

    @patch('src.master_recorder.asyncio.create_subprocess_exec')
    async def test_get_input_device_capabilities_fallback(self, mock_subprocess_exec):
        # Description: Verify fallback to default values when capabilities are not found.
        # Functionality: get_input_device_capabilities
        # Expected Outcome: Returns default max values (4095, 4095).
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b"some other output\n", b"")
        mock_subprocess_exec.return_value = mock_process

        with self.assertLogs('MasterRecorder', level='WARNING') as cm:
            capabilities = await get_input_device_capabilities("emulator-5554", "/dev/input/event0")
            self.assertIn("Could not find ABS_MT_POSITION_X or ABS_X max for /dev/input/event0.", cm.output[0])
            self.assertIn("Could not find ABS_MT_POSITION_Y or ABS_Y max for /dev/input/event0.", cm.output[1])
            self.assertIn("Final max X/Y capabilities are zero for /dev/input/event0. Falling back to defaults.", cm.output[2])
        self.assertEqual(capabilities["ABS_MT_POSITION_X_MAX"], 4095)
        self.assertEqual(capabilities["ABS_MT_POSITION_Y_MAX"], 4095)


    @patch('src.master_recorder.prompt_for_master_device', new_callable=AsyncMock, return_value="emulator-5554")
    @patch('src.master_recorder.connect_websocket', new_callable=AsyncMock)
    @patch('src.master_recorder.monitor_adb_events', new_callable=AsyncMock)
    async def test_main_loop_reconnects(self, mock_monitor_adb_events, mock_connect_websocket, mock_prompt):
        # Description: Verify that the main loop attempts to reconnect if monitoring fails.
        # Functionality: main
        # Expected Outcome: connect_websocket and monitor_adb_events are called multiple times.
        mock_ws = AsyncMock()
        mock_connect_websocket.side_effect = [mock_ws, mock_ws] # First connection succeeds, second also
        mock_monitor_adb_events.side_effect = [Exception("Monitor failed"), None] # First monitor fails, second succeeds

        # To prevent infinite loop, we'll run main as a task and cancel it after a few iterations
        main_task = asyncio.create_task(main())
        await asyncio.sleep(6) # Allow for one failure and one successful retry (5s sleep + processing)
        main_task.cancel()
        try:
            await main_task
        except asyncio.CancelledError:
            pass

        self.assertEqual(mock_connect_websocket.call_count, 2)
        self.assertEqual(mock_monitor_adb_events.call_count, 2)

if __name__ == '__main__':
    unittest.main()