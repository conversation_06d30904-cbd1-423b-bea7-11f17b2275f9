#!/usr/bin/env python3
"""
Simulate a tap event to test the complete pipeline.
This script sends a tap event directly to the farm device executor via WebSocket.
"""

import asyncio
import websockets
import json
import time

async def send_tap_event():
    """Send a tap event to the farm device executor"""
    try:
        # Connect to the central server as if we're a master device
        uri = "ws://127.0.0.1:8765/master"
        print(f"🔌 Connecting to central server: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to central server")
            
            # Send device info first
            device_info = {
                "action": "device_info",
                "device_id": "test-master",
                "model": "Test Master Device",
                "android_version": "11",
                "screen_width": 1080,
                "screen_height": 2400
            }
            
            await websocket.send(json.dumps(device_info))
            print("📱 Sent master device info")
            
            # Send master resolution update
            resolution_update = {
                "action": "master_resolution_update",
                "width": 1080,
                "height": 2400,
                "input_x_max": 32767,
                "input_y_max": 32767
            }
            
            await websocket.send(json.dumps(resolution_update))
            print("📏 Sent master resolution update")
            
            # Wait a moment for the resolution to be processed
            await asyncio.sleep(1)
            
            # Send a test tap event (center of screen)
            tap_event = {
                "action": "tap_release",  # Using tap_release since that's what we see in logs
                "x": 540,  # Center X of 1080px screen
                "y": 1200,  # Center Y of 2400px screen
                "raw_x": 16384,  # Half of 32767
                "raw_y": 16384,  # Half of 32767
                "device_path": "/dev/input/event2"
            }
            
            print(f"🎯 Sending test tap event: {tap_event}")
            await websocket.send(json.dumps(tap_event))
            print("✅ Tap event sent successfully")
            
            # Wait a moment to see if there's any response
            await asyncio.sleep(2)
            print("🏁 Test completed")
            
    except Exception as e:
        print(f"❌ Error: {e}")

async def monitor_logs():
    """Monitor farm device logs for the tap event"""
    print("\n📊 Monitoring farm device logs...")
    print("(Check the farm-device-executor.log file for detailed output)")
    
    # This would ideally tail the log file, but for simplicity we'll just check after
    await asyncio.sleep(3)
    
    import subprocess
    try:
        result = subprocess.run(
            ["tail", "-10", "farm-device-executor.log"], 
            capture_output=True, text=True, cwd="/home/<USER>/device-farm"
        )
        
        if result.returncode == 0:
            print("\n📋 Recent log entries:")
            lines = result.stdout.strip().split('\n')
            for line in lines[-5:]:  # Show last 5 lines
                if line.strip():
                    print(f"   {line}")
        else:
            print("❌ Could not read log file")
            
    except Exception as e:
        print(f"❌ Error reading logs: {e}")

async def main():
    print("🧪 Simulating Tap Event for Pipeline Testing")
    print("=" * 50)
    
    # Start log monitoring in background
    monitor_task = asyncio.create_task(monitor_logs())
    
    # Send the tap event
    await send_tap_event()
    
    # Wait for log monitoring to complete
    await monitor_task
    
    print("\n💡 What to look for:")
    print("   1. ✅ 'COORDINATE SCALING' messages showing coordinate transformation")
    print("   2. ✅ 'TAP EXECUTED' messages showing successful execution")
    print("   3. 🔧 'ADB COMMAND' messages (if enhanced logging is active)")
    print("   4. ❌ Any error messages indicating issues")
    
    print("\n📝 If you don't see the expected messages:")
    print("   1. The farm device executor may need to be restarted to pick up enhanced logging")
    print("   2. Check that the central server is properly routing messages")
    print("   3. Verify that farm devices are connected and registered")

if __name__ == "__main__":
    asyncio.run(main())
