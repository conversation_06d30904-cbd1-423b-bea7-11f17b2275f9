#!/usr/bin/env python3
"""
Integration test to verify the coordinate translation fix.
This script simulates the complete flow from master device to farm device.
"""

import asyncio
import json
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CoordinateTest")

class MockMasterDevice:
    """Simulates master device behavior"""
    
    def __init__(self, device_id: str, screen_width: int, screen_height: int, input_x_max: int, input_y_max: int):
        self.device_id = device_id
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.input_x_max = input_x_max
        self.input_y_max = input_y_max
    
    def get_resolution_data(self) -> Dict[str, Any]:
        """Get master resolution data"""
        return {
            "action": "master_resolution_update",
            "resolution": {
                "width": self.screen_width,
                "height": self.screen_height,
                "input_x_max": self.input_x_max,
                "input_y_max": self.input_y_max
            }
        }
    
    def convert_raw_to_screen(self, raw_x: int, raw_y: int) -> tuple:
        """Convert raw input coordinates to screen coordinates (master recorder logic)"""
        if self.input_x_max == 0 or self.input_y_max == 0:
            return raw_x, raw_y
        
        scaled_x = int((raw_x / self.input_x_max) * self.screen_width)
        scaled_y = int((raw_y / self.input_y_max) * self.screen_height)
        
        return scaled_x, scaled_y
    
    def create_tap_action(self, raw_x: int, raw_y: int, action_type: str) -> Dict[str, Any]:
        """Create a tap action with screen coordinates"""
        screen_x, screen_y = self.convert_raw_to_screen(raw_x, raw_y)
        
        return {
            "action": action_type,
            "x": screen_x,
            "y": screen_y,
            "raw_x": raw_x,
            "raw_y": raw_y,
            "device_path": "/dev/input/event2"
        }

class MockFarmDevice:
    """Simulates farm device behavior"""
    
    def __init__(self, device_id: str, screen_width: int, screen_height: int):
        self.device_id = device_id
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.master_resolution = None
    
    def update_master_resolution(self, resolution_data: Dict[str, Any]):
        """Update master resolution data"""
        if resolution_data.get("action") == "master_resolution_update":
            self.master_resolution = resolution_data.get("resolution", {})
            logger.info(f"📏 {self.device_id}: Updated master resolution: {self.master_resolution}")
    
    def scale_coordinates(self, x: int, y: int) -> tuple:
        """Scale coordinates from master device to farm device"""
        if not self.master_resolution:
            logger.warning(f"⚠️ {self.device_id}: Master resolution not available for scaling. Using raw coordinates: ({x}, {y})")
            return x, y
        
        # Check if we have all required fields
        required_fields = ["input_x_max", "input_y_max", "width", "height"]
        missing_fields = [field for field in required_fields if field not in self.master_resolution]
        if missing_fields:
            logger.error(f"❌ {self.device_id}: Master resolution missing fields {missing_fields}. Using raw coordinates: ({x}, {y})")
            return x, y
        
        master_width = self.master_resolution["width"]
        master_height = self.master_resolution["height"]
        
        if master_width == 0 or master_height == 0:
            logger.error(f"❌ {self.device_id}: Master screen dimensions are zero, cannot scale. Using raw coordinates: ({x}, {y})")
            return x, y
        
        # Scale from master screen coordinates to farm screen coordinates
        normalized_x = x / master_width
        normalized_y = y / master_height
        
        scaled_x = int(normalized_x * self.screen_width)
        scaled_y = int(normalized_y * self.screen_height)
        
        # Validate scaled coordinates are within farm device bounds
        if scaled_x < 0 or scaled_x > self.screen_width or scaled_y < 0 or scaled_y > self.screen_height:
            logger.warning(f"⚠️ {self.device_id}: Scaled coordinates ({scaled_x}, {scaled_y}) are outside farm device bounds {self.screen_width}x{self.screen_height}")
            # Clamp to bounds
            scaled_x = max(0, min(scaled_x, self.screen_width))
            scaled_y = max(0, min(scaled_y, self.screen_height))
            logger.info(f"🔧 {self.device_id}: Clamped coordinates to ({scaled_x}, {scaled_y})")
        
        logger.info(f"🎯 {self.device_id}: COORDINATE SCALING: ({x}, {y}) -> ({scaled_x}, {scaled_y})")
        return scaled_x, scaled_y
    
    def process_tap_action(self, action_data: Dict[str, Any]):
        """Process a tap action"""
        action_type = action_data.get("action")
        x = action_data.get("x")
        y = action_data.get("y")
        
        if x is not None and y is not None:
            logger.info(f"🔄 {self.device_id}: Processing {action_type} with original coordinates ({x}, {y})")
            scaled_x, scaled_y = self.scale_coordinates(x, y)
            logger.info(f"✅ {self.device_id}: Scaled coordinates from ({x}, {y}) to ({scaled_x}, {scaled_y})")
            return scaled_x, scaled_y
        else:
            logger.error(f"❌ {self.device_id}: {action_type} missing coordinates")
            return None, None

def test_coordinate_flow():
    """Test the complete coordinate flow"""
    print("🧪 Testing Complete Coordinate Flow")
    print("=" * 60)
    
    # Create mock devices
    master = MockMasterDevice(
        device_id="emulator-5554",
        screen_width=1080,
        screen_height=1920,
        input_x_max=32767,
        input_y_max=32767
    )
    
    farm1 = MockFarmDevice(
        device_id="emulator-5556",
        screen_width=720,
        screen_height=1280
    )
    
    farm2 = MockFarmDevice(
        device_id="emulator-5558",
        screen_width=1440,
        screen_height=3120
    )
    
    print(f"📱 Master: {master.device_id} ({master.screen_width}x{master.screen_height})")
    print(f"📱 Farm 1: {farm1.device_id} ({farm1.screen_width}x{farm1.screen_height})")
    print(f"📱 Farm 2: {farm2.device_id} ({farm2.screen_width}x{farm2.screen_height})")
    
    # Step 1: Send master resolution to farm devices
    print("\n📏 Step 1: Sending master resolution to farm devices")
    resolution_data = master.get_resolution_data()
    farm1.update_master_resolution(resolution_data)
    farm2.update_master_resolution(resolution_data)
    
    # Step 2: Test various tap scenarios
    test_cases = [
        ("Center tap", 16384, 16384),  # Center of screen
        ("Top-left corner", 0, 0),     # Top-left
        ("Bottom-right corner", 32767, 32767),  # Bottom-right
        ("Quarter point", 8192, 8192),  # Quarter point
    ]
    
    for test_name, raw_x, raw_y in test_cases:
        print(f"\n🎯 Step 2: Testing {test_name}")
        print(f"   Raw input coordinates: ({raw_x}, {raw_y})")
        
        # Master creates tap action
        tap_action = master.create_tap_action(raw_x, raw_y, "tap_press")
        print(f"   Master sends screen coordinates: ({tap_action['x']}, {tap_action['y']})")
        
        # Farm devices process the action
        farm1_x, farm1_y = farm1.process_tap_action(tap_action)
        farm2_x, farm2_y = farm2.process_tap_action(tap_action)
        
        print(f"   Farm 1 final coordinates: ({farm1_x}, {farm1_y})")
        print(f"   Farm 2 final coordinates: ({farm2_x}, {farm2_y})")
        
        # Validate proportional scaling
        if farm1_x is not None and farm2_x is not None:
            master_norm_x = tap_action['x'] / master.screen_width
            master_norm_y = tap_action['y'] / master.screen_height
            
            farm1_norm_x = farm1_x / farm1.screen_width
            farm1_norm_y = farm1_y / farm1.screen_height
            
            farm2_norm_x = farm2_x / farm2.screen_width
            farm2_norm_y = farm2_y / farm2.screen_height
            
            print(f"   Normalized coordinates:")
            print(f"     Master: ({master_norm_x:.3f}, {master_norm_y:.3f})")
            print(f"     Farm 1: ({farm1_norm_x:.3f}, {farm1_norm_y:.3f})")
            print(f"     Farm 2: ({farm2_norm_x:.3f}, {farm2_norm_y:.3f})")
            
            # Check if normalized coordinates match (within tolerance)
            tolerance = 0.01
            x_match = abs(master_norm_x - farm1_norm_x) < tolerance and abs(master_norm_x - farm2_norm_x) < tolerance
            y_match = abs(master_norm_y - farm1_norm_y) < tolerance and abs(master_norm_y - farm2_norm_y) < tolerance
            
            if x_match and y_match:
                print(f"   ✅ Scaling is accurate!")
            else:
                print(f"   ❌ Scaling mismatch detected!")
    
    print("\n🎉 Coordinate flow test completed!")

if __name__ == "__main__":
    test_coordinate_flow()
