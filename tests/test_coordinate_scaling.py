#!/usr/bin/env python3
"""
Test script to validate coordinate scaling logic between master and farm devices.
This script simulates the coordinate transformation process to ensure accuracy.
"""

def scale_raw_to_screen_coordinates(raw_x, raw_y, input_x_max, input_y_max, screen_width, screen_height):
    """Scale raw input coordinates to screen coordinates (master recorder logic)"""
    if input_x_max == 0 or input_y_max == 0:
        print(f"⚠️ Input max values are zero, cannot scale. Using raw coordinates: ({raw_x}, {raw_y})")
        return raw_x, raw_y  # Avoid division by zero
    
    # Scale from raw input range to screen pixel coordinates
    scaled_x = int((raw_x / input_x_max) * screen_width)
    scaled_y = int((raw_y / input_y_max) * screen_height)
    
    print(f"📐 Master scaling: raw ({raw_x}, {raw_y}) -> screen ({scaled_x}, {scaled_y})")
    return scaled_x, scaled_y

def scale_screen_to_screen_coordinates(master_x, master_y, master_width, master_height, farm_width, farm_height):
    """Scale screen coordinates from master to farm device (farm device logic)"""
    if master_width == 0 or master_height == 0:
        print(f"⚠️ Master screen dimensions are zero, cannot scale. Using original coordinates: ({master_x}, {master_y})")
        return master_x, master_y
    
    # Scale from master screen coordinates to farm screen coordinates
    normalized_x = master_x / master_width
    normalized_y = master_y / master_height
    
    scaled_x = int(normalized_x * farm_width)
    scaled_y = int(normalized_y * farm_height)
    
    print(f"🎯 Farm scaling: master screen ({master_x}, {master_y}) -> farm screen ({scaled_x}, {scaled_y})")
    return scaled_x, scaled_y

def test_coordinate_scaling():
    """Test coordinate scaling with various scenarios"""
    print("🧪 Testing Coordinate Scaling Logic")
    print("=" * 50)
    
    # Test scenario 1: Same resolution devices
    print("\n📱 Test 1: Same resolution devices (1080x1920)")
    master_input_x_max = 32767
    master_input_y_max = 32767
    master_width = 1080
    master_height = 1920
    farm_width = 1080
    farm_height = 1920
    
    # Simulate a tap in the center of the screen
    raw_x = 16384  # Half of input max
    raw_y = 16384  # Half of input max
    
    # Step 1: Master recorder converts raw to screen coordinates
    screen_x, screen_y = scale_raw_to_screen_coordinates(
        raw_x, raw_y, master_input_x_max, master_input_y_max, master_width, master_height
    )
    
    # Step 2: Farm device scales screen coordinates
    final_x, final_y = scale_screen_to_screen_coordinates(
        screen_x, screen_y, master_width, master_height, farm_width, farm_height
    )
    
    print(f"✅ Expected: center tap at ({farm_width//2}, {farm_height//2})")
    print(f"✅ Actual: ({final_x}, {final_y})")
    print(f"✅ Match: {abs(final_x - farm_width//2) <= 1 and abs(final_y - farm_height//2) <= 1}")
    
    # Test scenario 2: Different resolution devices
    print("\n📱 Test 2: Different resolution devices")
    print("   Master: 1080x1920, Farm: 720x1280")
    farm_width = 720
    farm_height = 1280
    
    # Step 1: Master recorder converts raw to screen coordinates (same as before)
    screen_x, screen_y = scale_raw_to_screen_coordinates(
        raw_x, raw_y, master_input_x_max, master_input_y_max, master_width, master_height
    )
    
    # Step 2: Farm device scales screen coordinates
    final_x, final_y = scale_screen_to_screen_coordinates(
        screen_x, screen_y, master_width, master_height, farm_width, farm_height
    )
    
    print(f"✅ Expected: center tap at ({farm_width//2}, {farm_height//2})")
    print(f"✅ Actual: ({final_x}, {final_y})")
    print(f"✅ Match: {abs(final_x - farm_width//2) <= 1 and abs(final_y - farm_height//2) <= 1}")
    
    # Test scenario 3: Corner tap
    print("\n📱 Test 3: Corner tap (top-left)")
    raw_x = 0
    raw_y = 0
    
    # Step 1: Master recorder converts raw to screen coordinates
    screen_x, screen_y = scale_raw_to_screen_coordinates(
        raw_x, raw_y, master_input_x_max, master_input_y_max, master_width, master_height
    )
    
    # Step 2: Farm device scales screen coordinates
    final_x, final_y = scale_screen_to_screen_coordinates(
        screen_x, screen_y, master_width, master_height, farm_width, farm_height
    )
    
    print(f"✅ Expected: top-left tap at (0, 0)")
    print(f"✅ Actual: ({final_x}, {final_y})")
    print(f"✅ Match: {final_x <= 1 and final_y <= 1}")
    
    # Test scenario 4: Bottom-right tap
    print("\n📱 Test 4: Bottom-right tap")
    raw_x = master_input_x_max
    raw_y = master_input_y_max
    
    # Step 1: Master recorder converts raw to screen coordinates
    screen_x, screen_y = scale_raw_to_screen_coordinates(
        raw_x, raw_y, master_input_x_max, master_input_y_max, master_width, master_height
    )
    
    # Step 2: Farm device scales screen coordinates
    final_x, final_y = scale_screen_to_screen_coordinates(
        screen_x, screen_y, master_width, master_height, farm_width, farm_height
    )
    
    print(f"✅ Expected: bottom-right tap at ({farm_width}, {farm_height})")
    print(f"✅ Actual: ({final_x}, {final_y})")
    print(f"✅ Match: {abs(final_x - farm_width) <= 1 and abs(final_y - farm_height) <= 1}")

if __name__ == "__main__":
    test_coordinate_scaling()
