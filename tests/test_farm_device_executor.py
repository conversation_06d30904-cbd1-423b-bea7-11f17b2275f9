import unittest
import asyncio
import json
import sys
import os
import time
from unittest.mock import AsyncMock, patch, MagicMock, call
from collections import defaultdict

import websockets

from src.farm_device_executor import FarmDeviceManager, DeviceStatus, DeviceInfo

class TestFarmDeviceExecutor(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.manager = FarmDeviceManager()
        # Reset internal state for each test
        self.manager.devices = {}
        self.manager.master_resolution = None
        self.manager.pending_taps = {}
        self.manager.master_device = None

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_discover_devices_happy_path(self, mock_subprocess_exec):
        # Description: Verify successful discovery of connected devices.
        # Functionality: discover_devices
        # Expected Outcome: Returns a list of device IDs.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (
            b"List of devices attached\nemulator-5554    device product:sdk_gphone64_x86_64 model:sdk_gphone64_x86_64 device:emulator_x86_64 transport_id:1\n"
            b"emulator-5556    device product:sdk_gphone64_x86_64 model:sdk_gphone64_x86_64 device:emulator_x86_64 transport_id:2\n",
            b""
        )
        mock_subprocess_exec.return_value = mock_process

        devices = await self.manager.discover_devices()
        self.assertEqual(devices, ["emulator-5554", "emulator-5556"])
        mock_subprocess_exec.assert_called_once_with('adb', 'devices', '-l', stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_discover_devices_no_devices(self, mock_subprocess_exec):
        # Description: Verify behavior when no devices are connected.
        # Functionality: discover_devices
        # Expected Outcome: Returns an empty list.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (b"List of devices attached\n", b"")
        mock_subprocess_exec.return_value = mock_process

        devices = await self.manager.discover_devices()
        self.assertEqual(devices, [])

    @patch('farm_device_executor.FarmDeviceManager.get_screen_resolution', new_callable=AsyncMock)
    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_get_device_info_happy_path(self, mock_subprocess_exec, mock_get_screen_resolution):
        # Description: Verify successful retrieval of device information.
        # Functionality: get_device_info
        # Expected Outcome: Returns a DeviceInfo object with correct details.
        mock_get_screen_resolution.return_value = {"width": 1080, "height": 1920}

        mock_model_process = AsyncMock()
        mock_model_process.communicate.return_value = (b"Pixel 4\n", b"")
        mock_version_process = AsyncMock()
        mock_version_process.communicate.return_value = (b"11\n", b"")

        mock_subprocess_exec.side_effect = [mock_model_process, mock_version_process]

        device_info = await self.manager.get_device_info("emulator-5554")
        self.assertIsNotNone(device_info)
        self.assertEqual(device_info.device_id, "emulator-5554")
        self.assertEqual(device_info.model, "Pixel 4")
        self.assertEqual(device_info.android_version, "11")
        self.assertEqual(device_info.screen_width, 1080)
        self.assertEqual(device_info.screen_height, 1920)
        self.assertEqual(device_info.status, DeviceStatus.DISCONNECTED)

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_get_screen_resolution_happy_path(self, mock_subprocess_exec):
        # Description: Verify successful retrieval of screen resolution.
        # Functionality: get_screen_resolution
        # Expected Outcome: Returns a dictionary with width and height.
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b"Physical size: 1080x1920\n", b"")
        mock_subprocess_exec.return_value = mock_process

        resolution = await self.manager.get_screen_resolution("emulator-5554")
        self.assertEqual(resolution, {"width": 1080, "height": 1920})

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_get_screen_resolution_parse_error(self, mock_subprocess_exec):
        # Description: Verify fallback behavior when screen resolution parsing fails.
        # Functionality: get_screen_resolution
        # Expected Outcome: Returns default resolution and logs a warning.
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b"Invalid output\n", b"")
        mock_subprocess_exec.return_value = mock_process

        with self.assertLogs('FarmDeviceExecutor', level='WARNING') as cm:
            resolution = await self.manager.get_screen_resolution("emulator-5554")
            self.assertIn("Could not parse screen resolution for emulator-5554: Invalid output", cm.output[0])
        self.assertEqual(resolution, {"width": 1080, "height": 1920})

    async def test_scale_coordinates_happy_path(self):
        # Description: Verify correct scaling of coordinates.
        # Functionality: scale_coordinates
        # Expected Outcome: Returns scaled X and Y coordinates.
        self.manager.master_resolution = {
            "width": 1080, "height": 1920, "input_x_max": 1200, "input_y_max": 2400
        }
        device_info = DeviceInfo(
            device_id="farm_device", model="Test", android_version="10",
            screen_width=720, screen_height=1280, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )

        # Test with screen coordinates (new logic)
        # Input coordinates are screen coordinates from master device
        scaled_x, scaled_y = self.manager.scale_coordinates(540, 960, device_info)
        # (540 / 1080) * 720 = 0.5 * 720 = 360
        # (960 / 1920) * 1280 = 0.5 * 1280 = 640
        self.assertEqual(scaled_x, 360)
        self.assertEqual(scaled_y, 640)

    async def test_scale_coordinates_no_master_resolution(self):
        # Description: Verify scaling fallback when master resolution is not set.
        # Functionality: scale_coordinates
        # Expected Outcome: Returns original coordinates and logs a warning.
        device_info = DeviceInfo(
            device_id="farm_device", model="Test", android_version="10",
            screen_width=720, screen_height=1280, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        with self.assertLogs('FarmDeviceExecutor', level='WARNING') as cm:
            scaled_x, scaled_y = self.manager.scale_coordinates(100, 200, device_info)
            self.assertIn("Master resolution not available for scaling. Using raw coordinates: (100, 200)", cm.output[0])
        self.assertEqual(scaled_x, 100)
        self.assertEqual(scaled_y, 200)

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_execute_tap_success(self, mock_subprocess_exec):
        # Description: Verify successful execution of a tap command.
        # Functionality: execute_tap
        # Expected Outcome: ADB command is called, returns True, status is CONNECTED.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (b"", b"")
        mock_subprocess_exec.return_value = mock_process

        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )

        result = await self.manager.execute_tap(100, 200, device_info)
        self.assertTrue(result)
        mock_subprocess_exec.assert_called_once_with(
            'adb', '-s', 'emulator-5554', 'shell', 'input', 'tap', '100', '200'
        )
        self.assertEqual(device_info.status, DeviceStatus.CONNECTED)
        self.assertEqual(device_info.error_count, 0)

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_execute_tap_failure(self, mock_subprocess_exec):
        # Description: Verify handling of failed tap command.
        # Functionality: execute_tap
        # Expected Outcome: ADB command is called, returns False, status is ERROR, error_count increments.
        mock_process = AsyncMock()
        mock_process.returncode = 1
        mock_process.communicate.return_value = (b"", b"Error executing tap")
        mock_subprocess_exec.return_value = mock_process

        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )

        result = await self.manager.execute_tap(100, 200, device_info)
        self.assertFalse(result)
        self.assertEqual(device_info.status, DeviceStatus.ERROR)
        self.assertEqual(device_info.error_count, 1)

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_execute_key_press_success(self, mock_subprocess_exec):
        # Description: Verify successful execution of a key press command.
        # Functionality: execute_key_press
        # Expected Outcome: ADB command is called with correct key code, returns True.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (b"", b"")
        mock_subprocess_exec.return_value = mock_process

        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )

        result = await self.manager.execute_key_press("KEY_HOME", device_info)
        self.assertTrue(result)
        mock_subprocess_exec.assert_called_once_with(
            'adb', '-s', 'emulator-5554', 'shell', 'input', 'keyevent', '3'
        )
        self.assertEqual(device_info.status, DeviceStatus.CONNECTED)
        self.assertEqual(device_info.error_count, 0)

    @patch('farm_device_executor.asyncio.create_subprocess_exec')
    async def test_execute_text_input_success(self, mock_subprocess_exec):
        # Description: Verify successful execution of a text input command.
        # Functionality: execute_text_input
        # Expected Outcome: ADB command is called with escaped text, returns True.
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (b"", b"")
        mock_subprocess_exec.return_value = mock_process

        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        test_text = "Hello World!"
        escaped_text = "Hello%sWorld!"

        result = await self.manager.execute_text_input(test_text, device_info)
        self.assertTrue(result)
        mock_subprocess_exec.assert_called_once_with(
            'adb', '-s', 'emulator-5554', 'shell', 'input', 'text', escaped_text
        )
        self.assertEqual(device_info.status, DeviceStatus.CONNECTED)
        self.assertEqual(device_info.error_count, 0)

    async def test_process_action_tap_press_and_release(self):
        # Description: Verify processing of tap_press and tap_release actions.
        # Functionality: process_action, execute_tap
        # Expected Outcome: Tap is executed only after tap_release.
        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        self.manager.master_resolution = {
            "width": 1080, "height": 1920, "input_x_max": 1200, "input_y_max": 2400
        }

        with patch('farm_device_executor.FarmDeviceManager.execute_tap', new_callable=AsyncMock) as mock_execute_tap:
            # Simulate tap_press
            await self.manager.process_action({"action": "tap_press", "x": 600, "y": 1200}, device_info)
            mock_execute_tap.assert_not_called()
            self.assertIn("emulator-5554", self.manager.pending_taps)
            self.assertEqual(self.manager.pending_taps["emulator-5554"], {'x': 540, 'y': 640}) # Scaled coordinates

            # Simulate tap_release
            await self.manager.process_action({"action": "tap_release", "x": 600, "y": 1200}, device_info)
            mock_execute_tap.assert_called_once_with(540, 640, device_info)
            self.assertNotIn("emulator-5554", self.manager.pending_taps)

    async def test_process_action_key_press(self):
        # Description: Verify processing of key_press action.
        # Functionality: process_action, execute_key_press
        # Expected Outcome: Key press is executed.
        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        with patch('farm_device_executor.FarmDeviceManager.execute_key_press', new_callable=AsyncMock) as mock_execute_key_press:
            await self.manager.process_action({"action": "key_press", "key_code": "KEY_BACK"}, device_info)
            mock_execute_key_press.assert_called_once_with("KEY_BACK", device_info)

    async def test_process_action_text_input(self):
        # Description: Verify processing of text_input action.
        # Functionality: process_action, execute_text_input
        # Expected Outcome: Text input is executed.
        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        with patch('farm_device_executor.FarmDeviceManager.execute_text_input', new_callable=AsyncMock) as mock_execute_text_input:
            await self.manager.process_action({"action": "text_input", "text": "hello"}, device_info)
            mock_execute_text_input.assert_called_once_with("hello", device_info)

    async def test_process_action_master_resolution_update(self):
        # Description: Verify processing of master_resolution_update action.
        # Functionality: process_action
        # Expected Outcome: manager.master_resolution is updated.
        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        resolution_data = {
            "action": "master_resolution_update",
            "resolution": {"width": 1440, "height": 2560, "input_x_max": 1500, "input_y_max": 3000}
        }
        await self.manager.process_action(resolution_data, device_info)
        self.assertEqual(self.manager.master_resolution, resolution_data["resolution"])

    @patch('farm_device_executor.websockets.connect', new_callable=AsyncMock)
    async def test_connect_websocket_success(self, mock_websocket_connect):
        # Description: Verify successful WebSocket connection.
        # Functionality: connect_websocket
        # Expected Outcome: Returns a websocket object.
        mock_ws = AsyncMock()
        mock_websocket_connect.return_value = mock_ws
        
        websocket = await self.manager.connect_websocket()
        self.assertEqual(websocket, mock_ws)
        mock_websocket_connect.assert_called_once_with(self.manager.central_server_url)

    @patch('farm_device_executor.websockets.connect', new_callable=AsyncMock)
    async def test_connect_websocket_retry_and_failure(self, mock_websocket_connect):
        # Description: Verify retry logic and eventual failure for WebSocket connection.
        # Functionality: connect_websocket
        # Expected Outcome: Logs errors and returns None after max retries.
        mock_websocket_connect.side_effect = [
            asyncio.TimeoutError,
            websockets.exceptions.ConnectionClosed,
            Exception("Test Error"),
            asyncio.TimeoutError,
            websockets.exceptions.ConnectionClosed,
            Exception("Final Test Error") # This will be the last attempt
        ]
        self.manager.max_retry_attempts = 3 # Set a lower retry count for testing
        self.manager.retry_delay = 0.01 # Speed up retries

        with self.assertLogs('FarmDeviceExecutor', level='ERROR') as cm:
            websocket = await self.manager.connect_websocket()
            self.assertIsNone(websocket)
            self.assertEqual(mock_websocket_connect.call_count, 3) # Should try 3 times
            self.assertIn("Failed to connect after all retry attempts", cm.output[-1])

    @patch('farm_device_executor.FarmDeviceManager.connect_websocket', new_callable=AsyncMock)
    async def test_handle_device_connection_happy_path(self, mock_connect_websocket):
        # Description: Verify full device connection lifecycle.
        # Functionality: handle_device_connection
        # Expected Outcome: Device connects, sends info, receives and processes action.
        mock_ws = AsyncMock()
        mock_ws.__aiter__.return_value = [json.dumps({"action": "key_press", "key_code": "KEY_HOME"})]
        mock_connect_websocket.return_value = mock_ws

        device_info = DeviceInfo(
            device_id="emulator-5554", model="Test", android_version="10",
            screen_width=1080, screen_height=1920, status=DeviceStatus.DISCONNECTED, last_seen=time.time()
        )

        with patch('farm_device_executor.FarmDeviceManager.send_device_info', new_callable=AsyncMock) as mock_send_info, \
             patch('farm_device_executor.FarmDeviceManager.process_action', new_callable=AsyncMock) as mock_process_action:
            
            task = asyncio.create_task(self.manager.handle_device_connection(device_info))
            await asyncio.sleep(0.1) # Allow connection and first message to process
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

            self.assertEqual(device_info.status, DeviceStatus.CONNECTED)
            mock_send_info.assert_called_once_with(mock_ws, device_info)
            mock_process_action.assert_called_once_with({"action": "key_press", "key_code": "KEY_HOME"}, device_info)
            self.assertEqual(device_info.error_count, 0)

    @patch('farm_device_executor.FarmDeviceManager.discover_devices', new_callable=AsyncMock)
    @patch('farm_device_executor.FarmDeviceManager.get_device_info', new_callable=AsyncMock)
    @patch('farm_device_executor.asyncio.create_task')
    async def test_monitor_device_health_add_new_device(self, mock_create_task, mock_get_device_info, mock_discover_devices):
        # Description: Verify that new devices are detected and connection tasks are created.
        # Functionality: monitor_device_health
        # Expected Outcome: New device is added to manager.devices and a task is created for it.
        mock_discover_devices.side_effect = [
            ["emulator-5554"], # Initial discovery
            ["emulator-5554", "emulator-5556"] # Second discovery with new device
        ]
        mock_get_device_info.return_value = DeviceInfo(
            device_id="emulator-5556", model="New Device", android_version="12",
            screen_width=100, screen_height=200, status=DeviceStatus.DISCONNECTED, last_seen=time.time()
        )
        
        # Simulate an existing device
        self.manager.devices["emulator-5554"] = DeviceInfo(
            device_id="emulator-5554", model="Old Device", android_version="11",
            screen_width=100, screen_height=200, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )

        # Run monitor_device_health for a short period
        monitor_task = asyncio.create_task(self.manager.monitor_device_health())
        await asyncio.sleep(self.manager.device_check_interval * 1.5) # Allow two discovery cycles
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

        self.assertIn("emulator-5556", self.manager.devices)
        mock_create_task.assert_called_once() # Should be called for the new device

    @patch('farm_device_executor.FarmDeviceManager.discover_devices', new_callable=AsyncMock)
    @patch('farm_device_executor.FarmDeviceManager.get_device_info', new_callable=AsyncMock)
    async def test_monitor_device_health_remove_disconnected_device(self, mock_get_device_info, mock_discover_devices):
        # Description: Verify that disconnected devices are marked as such.
        # Functionality: monitor_device_health
        # Expected Outcome: Device status changes to DISCONNECTED.
        mock_discover_devices.side_effect = [
            ["emulator-5554", "emulator-5556"], # Initial discovery
            ["emulator-5554"] # Second discovery, one device missing
        ]
        
        # Simulate existing devices
        self.manager.devices["emulator-5554"] = DeviceInfo(
            device_id="emulator-5554", model="Device A", android_version="11",
            screen_width=100, screen_height=200, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )
        self.manager.devices["emulator-5556"] = DeviceInfo(
            device_id="emulator-5556", model="Device B", android_version="11",
            screen_width=100, screen_height=200, status=DeviceStatus.CONNECTED, last_seen=time.time()
        )

        monitor_task = asyncio.create_task(self.manager.monitor_device_health())
        await asyncio.sleep(self.manager.device_check_interval * 1.5)
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

        self.assertEqual(self.manager.devices["emulator-5554"].status, DeviceStatus.CONNECTED)
        self.assertEqual(self.manager.devices["emulator-5556"].status, DeviceStatus.DISCONNECTED)

    @patch('builtins.input', side_effect=['emulator-5554'])
    @patch('builtins.print')
    @patch('farm_device_executor.FarmDeviceManager.discover_devices', new_callable=AsyncMock)
    async def test_prompt_for_master_device_valid_input(self, mock_discover_devices, mock_print, mock_input):
        # Description: Verify valid master device selection via prompt.
        # Functionality: prompt_for_master_device
        # Expected Outcome: Returns the selected device ID.
        mock_discover_devices.return_value = ["emulator-5554", "emulator-5556"]
        
        master_device = self.manager.prompt_for_master_device(["emulator-5554", "emulator-5556"])
        self.assertEqual(master_device, "emulator-5554")
        mock_input.assert_called_once()

    @patch('builtins.input', side_effect=['invalid', 'n'])
    @patch('builtins.print')
    @patch('farm_device_executor.FarmDeviceManager.discover_devices', new_callable=AsyncMock)
    async def test_prompt_for_master_device_invalid_input_and_exit(self, mock_discover_devices, mock_print, mock_input):
        # Description: Verify handling of invalid master device input and user choosing to exit.
        # Functionality: prompt_for_master_device
        # Expected Outcome: Returns "EXIT".
        mock_discover_devices.return_value = ["emulator-5554", "emulator-5556"]
        
        master_device = self.manager.prompt_for_master_device(["emulator-5554", "emulator-5556"])
        self.assertEqual(master_device, "EXIT")
        self.assertEqual(mock_input.call_count, 2) # One for device ID, one for retry

    @patch('farm_device_executor.FarmDeviceManager.prompt_for_master_device', return_value=None)
    @patch('farm_device_executor.FarmDeviceManager.discover_devices', new_callable=AsyncMock)
    @patch('farm_device_executor.FarmDeviceManager.get_device_info', new_callable=AsyncMock)
    @patch('farm_device_executor.asyncio.create_task')
    @patch('farm_device_executor.asyncio.gather', new_callable=AsyncMock)
    async def test_run_no_master_auto_detect_fail(self, mock_gather, mock_create_task, mock_get_device_info, mock_discover_devices, mock_prompt):
        # Description: Verify run method when no master is specified and auto-detection fails.
        # Functionality: run
        # Expected Outcome: All discovered devices are treated as farm devices.
        mock_discover_devices.return_value = ["emulator-5554", "emulator-5556"]
        mock_get_device_info.side_effect = [
            DeviceInfo("emulator-5554", "Model A", "10", 100, 200, DeviceStatus.DISCONNECTED, time.time()),
            DeviceInfo("emulator-5556", "Model B", "10", 100, 200, DeviceStatus.DISCONNECTED, time.time())
        ]
        
        # Mock ps aux to not find master-recorder.py
        with patch('farm_device_executor.asyncio.create_subprocess_exec') as mock_ps_exec:
            mock_ps_process = AsyncMock()
            mock_ps_process.communicate.return_value = (b"some other process output\n", b"")
            mock_ps_exec.return_value = mock_ps_process
            
            await self.manager.run()

            self.assertIn("emulator-5554", self.manager.devices)
            self.assertIn("emulator-5556", self.manager.devices)
            self.assertEqual(len(self.manager.devices), 2)
            self.assertIsNone(self.manager.master_device) # No master detected
            self.assertEqual(mock_create_task.call_count, 4) # 2 device tasks + monitor + print_status

    @patch('farm_device_executor.FarmDeviceManager.prompt_for_master_device', return_value="emulator-5554")
    @patch('farm_device_executor.FarmDeviceManager.discover_devices', new_callable=AsyncMock)
    @patch('farm_device_executor.FarmDeviceManager.get_device_info', new_callable=AsyncMock)
    @patch('farm_device_executor.asyncio.create_task')
    @patch('farm_device_executor.asyncio.gather', new_callable=AsyncMock)
    @patch('farm_device_executor.asyncio.create_subprocess_exec') # For adb getprop ro.product.model
    async def test_run_with_specified_master(self, mock_adb_getprop, mock_gather, mock_create_task, mock_get_device_info, mock_discover_devices, mock_prompt):
        # Description: Verify run method correctly excludes a specified master device.
        # Functionality: run
        # Expected Outcome: Master device is excluded, only farm devices are managed.
        mock_discover_devices.return_value = ["emulator-5554", "emulator-5556"]
        mock_get_device_info.side_effect = [
            DeviceInfo("emulator-5556", "Model B", "10", 100, 200, DeviceStatus.DISCONNECTED, time.time())
        ]
        
        mock_adb_getprop_process = AsyncMock()
        mock_adb_getprop_process.communicate.return_value = (b"Pixel 4\n", b"")
        mock_adb_getprop.return_value = mock_adb_getprop_process

        await self.manager.run(master_device="emulator-5554")

        self.assertEqual(self.manager.master_device, "emulator-5554")
        self.assertNotIn("emulator-5554", self.manager.devices)
        self.assertIn("emulator-5556", self.manager.devices)
        self.assertEqual(len(self.manager.devices), 1)
        self.assertEqual(mock_create_task.call_count, 3) # 1 device task + monitor + print_status

if __name__ == '__main__':
    unittest.main()