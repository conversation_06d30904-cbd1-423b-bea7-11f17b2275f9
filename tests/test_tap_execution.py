#!/usr/bin/env python3
"""
Comprehensive test script to verify tap execution pipeline.
This script tests the complete flow from coordinate scaling to ADB execution.
"""

import subprocess
import asyncio
import json
import time
from typing import List, Dict, Tuple

def run_adb_command(cmd: List[str]) -> Tuple[int, str, str]:
    """Run an ADB command and return return code, stdout, stderr"""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def get_device_screen_info(device_id: str) -> Dict[str, int]:
    """Get device screen resolution and other info"""
    print(f"\n📱 Getting screen info for {device_id}:")
    
    # Get screen size
    cmd = ['adb', '-s', device_id, 'shell', 'wm', 'size']
    returncode, stdout, stderr = run_adb_command(cmd)
    
    if returncode == 0 and "Physical size:" in stdout:
        size_line = stdout.split("Physical size:")[1].strip()
        width, height = map(int, size_line.split('x'))
        print(f"   Screen resolution: {width}x{height}")
        return {"width": width, "height": height}
    else:
        print(f"   ❌ Failed to get screen size: {stderr}")
        return {"width": 1080, "height": 1920}  # Default

def test_adb_connectivity():
    """Test basic ADB connectivity"""
    print("🔧 Testing ADB Connectivity")
    print("-" * 40)
    
    # Check ADB devices
    cmd = ['adb', 'devices']
    returncode, stdout, stderr = run_adb_command(cmd)
    
    if returncode != 0:
        print(f"❌ ADB devices command failed: {stderr}")
        return []
    
    devices = []
    lines = stdout.split('\n')[1:]  # Skip header
    for line in lines:
        if line.strip() and '\tdevice' in line:
            device_id = line.split('\t')[0]
            devices.append(device_id)
            print(f"   ✅ {device_id}: Connected")
    
    if not devices:
        print("   ❌ No devices found")
    
    return devices

def test_manual_tap_commands(devices: List[str]):
    """Test manual ADB tap commands"""
    print("\n🎯 Testing Manual ADB Tap Commands")
    print("-" * 40)
    
    for device_id in devices:
        print(f"\n📱 Testing {device_id}:")
        screen_info = get_device_screen_info(device_id)
        
        # Test center tap
        center_x = screen_info["width"] // 2
        center_y = screen_info["height"] // 2
        
        test_coordinates = [
            (center_x, center_y, "Center"),
            (100, 100, "Top-left area"),
            (screen_info["width"] - 100, screen_info["height"] - 100, "Bottom-right area"),
        ]
        
        for x, y, description in test_coordinates:
            cmd = ['adb', '-s', device_id, 'shell', 'input', 'tap', str(x), str(y)]
            print(f"   🔧 Testing {description} tap at ({x}, {y})")
            print(f"      Command: {' '.join(cmd)}")
            
            returncode, stdout, stderr = run_adb_command(cmd)
            
            if returncode == 0:
                print(f"      ✅ Success")
                if stdout:
                    print(f"      📤 Stdout: {stdout}")
            else:
                print(f"      ❌ Failed (Return Code: {returncode})")
                if stderr:
                    print(f"      📤 Stderr: {stderr}")
            
            time.sleep(0.5)  # Small delay between taps

def test_coordinate_scaling_accuracy():
    """Test coordinate scaling accuracy with known values"""
    print("\n📐 Testing Coordinate Scaling Accuracy")
    print("-" * 40)
    
    # Test scenarios based on actual device resolutions
    master_res = {"width": 1080, "height": 2400}
    farm_res = {"width": 1440, "height": 3120}
    
    test_cases = [
        (540, 1200, "Center"),
        (0, 0, "Top-left"),
        (1080, 2400, "Bottom-right"),
        (270, 600, "Quarter point"),
        (810, 1800, "Three-quarter point"),
    ]
    
    print(f"   Master resolution: {master_res['width']}x{master_res['height']}")
    print(f"   Farm resolution: {farm_res['width']}x{farm_res['height']}")
    print(f"   Scaling ratio: {farm_res['width']/master_res['width']:.3f}x, {farm_res['height']/master_res['height']:.3f}y")
    print()
    
    for master_x, master_y, description in test_cases:
        # Calculate expected farm coordinates
        normalized_x = master_x / master_res["width"]
        normalized_y = master_y / master_res["height"]
        
        expected_farm_x = int(normalized_x * farm_res["width"])
        expected_farm_y = int(normalized_y * farm_res["height"])
        
        print(f"   {description}:")
        print(f"      Master: ({master_x}, {master_y})")
        print(f"      Expected Farm: ({expected_farm_x}, {expected_farm_y})")
        print(f"      Normalized: ({normalized_x:.3f}, {normalized_y:.3f})")

def analyze_recent_tap_execution():
    """Analyze recent tap execution from logs"""
    print("\n📊 Analyzing Recent Tap Execution")
    print("-" * 40)
    
    try:
        # Read recent farm device logs
        result = subprocess.run(
            ["tail", "-30", "farm-device-executor.log"], 
            capture_output=True, text=True, cwd="/home/<USER>/device-farm"
        )
        
        if result.returncode != 0:
            print("   ❌ Could not read farm device logs")
            return
        
        lines = result.stdout.split('\n')
        
        # Count different types of events
        tap_executed = 0
        tap_failed = 0
        coordinate_scaling = 0
        adb_commands = 0
        
        recent_coordinates = []
        
        for line in lines:
            if "✅" in line and "Executed tap at" in line:
                tap_executed += 1
                # Extract coordinates
                import re
                match = re.search(r'at \((\d+), (\d+)\)', line)
                if match:
                    x, y = map(int, match.groups())
                    recent_coordinates.append((x, y))
            elif "❌" in line and "tap" in line.lower():
                tap_failed += 1
            elif "COORDINATE SCALING" in line:
                coordinate_scaling += 1
            elif "ADB COMMAND" in line or "Executing command" in line:
                adb_commands += 1
        
        print(f"   📈 Recent activity (last 30 log lines):")
        print(f"      ✅ Successful taps: {tap_executed}")
        print(f"      ❌ Failed taps: {tap_failed}")
        print(f"      📐 Coordinate scaling events: {coordinate_scaling}")
        print(f"      🔧 ADB commands: {adb_commands}")
        
        if recent_coordinates:
            print(f"   📍 Recent tap coordinates:")
            for i, (x, y) in enumerate(recent_coordinates[-5:], 1):  # Show last 5
                print(f"      {i}. ({x}, {y})")
        
        # Check for any error patterns
        error_patterns = ["Failed", "Error", "Exception", "timeout"]
        errors_found = []
        
        for line in lines:
            for pattern in error_patterns:
                if pattern.lower() in line.lower() and "tap" in line.lower():
                    errors_found.append(line.strip())
        
        if errors_found:
            print(f"   ⚠️ Potential issues found:")
            for error in errors_found[-3:]:  # Show last 3 errors
                print(f"      - {error}")
        else:
            print(f"   ✅ No obvious errors found in recent logs")
            
    except Exception as e:
        print(f"   ❌ Error analyzing logs: {e}")

def test_device_responsiveness(devices: List[str]):
    """Test if devices are responsive to input"""
    print("\n📱 Testing Device Responsiveness")
    print("-" * 40)
    
    for device_id in devices:
        print(f"\n   Testing {device_id}:")
        
        # Test if device is awake and responsive
        cmd = ['adb', '-s', device_id, 'shell', 'dumpsys', 'power', '|', 'grep', 'mWakefulness']
        returncode, stdout, stderr = run_adb_command(['adb', '-s', device_id, 'shell', 'dumpsys power | grep mWakefulness'])
        
        if returncode == 0:
            if "Awake" in stdout:
                print(f"      ✅ Device is awake and responsive")
            else:
                print(f"      ⚠️ Device may be sleeping: {stdout}")
        else:
            print(f"      ❓ Could not determine device state")
        
        # Test input system
        cmd = ['adb', '-s', device_id, 'shell', 'getevent', '-t']
        print(f"      🔧 Input system test (checking if getevent works)...")
        
        try:
            # Start getevent and immediately terminate to test if it works
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            time.sleep(0.5)  # Let it start
            process.terminate()
            process.wait(timeout=2)
            print(f"      ✅ Input system accessible")
        except Exception as e:
            print(f"      ❌ Input system issue: {e}")

def main():
    print("🧪 Comprehensive Tap Execution Pipeline Test")
    print("=" * 60)
    
    # Test 1: ADB Connectivity
    devices = test_adb_connectivity()
    if not devices:
        print("\n❌ No devices available for testing")
        return
    
    # Test 2: Device Responsiveness
    test_device_responsiveness(devices)
    
    # Test 3: Manual ADB Tap Commands
    test_manual_tap_commands(devices)
    
    # Test 4: Coordinate Scaling Accuracy
    test_coordinate_scaling_accuracy()
    
    # Test 5: Recent Tap Execution Analysis
    analyze_recent_tap_execution()
    
    print("\n🎯 Summary and Recommendations:")
    print("-" * 40)
    print("1. ✅ If manual ADB taps work, the issue is not with ADB connectivity")
    print("2. ✅ If coordinate scaling is accurate, the issue is not with scaling logic")
    print("3. 🔧 Check the enhanced logging in farm device executor for detailed ADB command info")
    print("4. 📱 Verify that the farm device screen is visible and unlocked")
    print("5. 🎯 Test by tapping on the master device and observing farm device logs")
    
    print(f"\n📝 Next steps:")
    print(f"   1. Monitor farm device logs with: tail -f farm-device-executor.log")
    print(f"   2. Tap on master device and watch for ADB command execution")
    print(f"   3. Verify taps are visible on farm device screen")

if __name__ == "__main__":
    main()
