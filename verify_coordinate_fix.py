#!/usr/bin/env python3
"""
Verification script to test the coordinate translation fixes.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import asyncio
import time
from farm_device_executor import FarmDeviceManager, DeviceInfo, DeviceStatus

def test_coordinate_scaling():
    """Test the coordinate scaling functionality"""
    print("🧪 Testing Coordinate Scaling Fixes")
    print("=" * 50)
    
    # Create farm device manager
    manager = FarmDeviceManager("ws://127.0.0.1:8765/farm")
    
    # Create device info
    device_info = DeviceInfo(
        device_id="test_device",
        model="Test Device",
        android_version="10",
        screen_width=720,
        screen_height=1280,
        status=DeviceStatus.CONNECTED,
        last_seen=time.time()
    )
    
    # Test 1: No master resolution
    print("\n📱 Test 1: No master resolution")
    manager.master_resolution = None
    scaled_x, scaled_y = manager.scale_coordinates(100, 200, device_info)
    print(f"   Input: (100, 200)")
    print(f"   Output: ({scaled_x}, {scaled_y})")
    print(f"   Expected: (100, 200) - ✅" if scaled_x == 100 and scaled_y == 200 else "   Expected: (100, 200) - ❌")
    
    # Test 2: With master resolution - screen coordinates
    print("\n📱 Test 2: With master resolution (screen coordinates)")
    manager.master_resolution = {
        "width": 1080,
        "height": 1920,
        "input_x_max": 32767,
        "input_y_max": 32767
    }
    
    # Test center tap (screen coordinates from master)
    scaled_x, scaled_y = manager.scale_coordinates(540, 960, device_info)
    print(f"   Input: (540, 960) [master screen center]")
    print(f"   Output: ({scaled_x}, {scaled_y})")
    print(f"   Expected: (360, 640) [farm screen center] - ✅" if scaled_x == 360 and scaled_y == 640 else f"   Expected: (360, 640) [farm screen center] - ❌")
    
    # Test 3: Master resolution update
    print("\n📱 Test 3: Master resolution update")
    resolution_data = {
        "action": "master_resolution_update",
        "resolution": {
            "width": 1440,
            "height": 2560,
            "input_x_max": 1500,
            "input_y_max": 3000
        }
    }
    
    async def test_resolution_update():
        await manager.process_action(resolution_data, device_info)
        return manager.master_resolution
    
    updated_resolution = asyncio.run(test_resolution_update())
    print(f"   Updated resolution: {updated_resolution}")
    expected_resolution = resolution_data["resolution"]
    print(f"   Expected: {expected_resolution}")
    print(f"   Match: ✅" if updated_resolution == expected_resolution else "   Match: ❌")
    
    # Test 4: Coordinate scaling with updated resolution
    print("\n📱 Test 4: Coordinate scaling with updated resolution")
    # Test center tap with new resolution
    scaled_x, scaled_y = manager.scale_coordinates(720, 1280, device_info)
    print(f"   Input: (720, 1280) [master screen center]")
    print(f"   Output: ({scaled_x}, {scaled_y})")
    print(f"   Expected: (360, 640) [farm screen center] - ✅" if scaled_x == 360 and scaled_y == 640 else f"   Expected: (360, 640) [farm screen center] - ❌")
    
    # Test 5: Edge cases
    print("\n📱 Test 5: Edge cases")
    
    # Top-left corner
    scaled_x, scaled_y = manager.scale_coordinates(0, 0, device_info)
    print(f"   Top-left (0, 0) -> ({scaled_x}, {scaled_y}) - ✅" if scaled_x == 0 and scaled_y == 0 else f"   Top-left (0, 0) -> ({scaled_x}, {scaled_y}) - ❌")
    
    # Bottom-right corner
    scaled_x, scaled_y = manager.scale_coordinates(1440, 2560, device_info)
    print(f"   Bottom-right (1440, 2560) -> ({scaled_x}, {scaled_y}) - ✅" if scaled_x == 720 and scaled_y == 1280 else f"   Bottom-right (1440, 2560) -> ({scaled_x}, {scaled_y}) - ❌")
    
    print("\n🎉 Coordinate scaling tests completed!")

if __name__ == "__main__":
    test_coordinate_scaling()
